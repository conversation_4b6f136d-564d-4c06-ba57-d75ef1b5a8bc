#!/usr/bin/env python3
"""
Mock demo for Multi-Agent Intent Detection Framework
This demo simulates LLM responses to show the framework workflow
"""
import os
import sys
import time
from pathlib import Path
import random
from loguru import logger

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.config import load_config
from src.utils.data_processor import create_data_processor
from src.agents.base_agent import Principle, IntentExample
from src.evaluation.metrics import ResultsAnalyzer


class MockLLMClient:
    """Mock LLM client that simulates responses"""
    
    def __init__(self):
        self.call_count = 0
        
    def generate(self, prompt, config=None):
        """Generate mock response based on prompt type"""
        self.call_count += 1
        
        if "extract principles" in prompt.lower() or "analytical" in prompt.lower():
            return self._generate_analytical_principles()
        elif "pattern" in prompt.lower():
            return self._generate_pattern_principles()
        elif "linguistic" in prompt.lower():
            return self._generate_linguistic_principles()
        elif "contextual" in prompt.lower():
            return self._generate_contextual_principles()
        elif "evaluate" in prompt.lower() and "adequacy" in prompt.lower():
            return str(random.uniform(0.6, 0.9))
        elif "evaluate" in prompt.lower() and "clarity" in prompt.lower():
            return str(random.uniform(0.6, 0.9))
        elif "consolidate" in prompt.lower():
            return self._generate_consolidated_principles()
        elif "classify" in prompt.lower():
            return self._generate_classification()
        else:
            return "Mock response for: " + prompt[:50] + "..."
            
    def _generate_analytical_principles(self):
        return """PRINCIPLE 1: Technical domain keywords are the primary indicators of intent, such as "WordPress", "Oracle", "SVN" appearing directly in the text.

PRINCIPLE 2: Question patterns reveal intent - configuration questions indicate setup/admin intent, while troubleshooting questions indicate problem-solving intent.

PRINCIPLE 3: Technology-specific terminology and jargon help distinguish between different technical domains and their associated intents.

PRINCIPLE 4: Context clues like version numbers, error messages, and specific features help narrow down the exact technology and intent.

PRINCIPLE 5: Action verbs in questions (configure, install, debug, optimize) provide insight into the user's specific intent within a domain."""
        
    def _generate_pattern_principles(self):
        return """PATTERN 1: Direct technology mentions in question titles strongly correlate with intent labels (e.g., "WordPress plugin" → wordpress).

PATTERN 2: Error message patterns and stack traces are distinctive markers for specific technologies and frameworks.

PATTERN 3: File extension patterns (.php for WordPress, .sql for Oracle, .scala for Scala) provide clear intent indicators.

PATTERN 4: API and method name patterns are unique to specific technologies and can reliably identify intent.

PATTERN 5: Configuration syntax patterns help distinguish between similar technologies in the same domain."""
        
    def _generate_linguistic_principles(self):
        return """LINGUISTIC_FEATURE 1: Domain-specific vocabulary creates semantic fields that cluster around particular technologies and intents.

LINGUISTIC_FEATURE 2: Syntactic patterns in technical questions follow predictable structures that correlate with specific domains.

LINGUISTIC_FEATURE 3: Named entity patterns including software names, version numbers, and technical terms are strong intent indicators.

LINGUISTIC_FEATURE 4: Discourse markers and question types reveal the pragmatic intent behind technical queries.

LINGUISTIC_FEATURE 5: Morphological patterns in technical terminology help distinguish between related but distinct technologies."""
        
    def _generate_contextual_principles(self):
        return """CONTEXT_PRINCIPLE 1: Problem-solving contexts reveal intent through the type of issue being addressed and the technical environment.

CONTEXT_PRINCIPLE 2: Development workflow contexts indicate intent based on the stage of development and tools being used.

CONTEXT_PRINCIPLE 3: Platform and environment indicators provide crucial context for determining the specific technology intent.

CONTEXT_PRINCIPLE 4: User expertise level contexts influence how technical questions are framed and what details are included.

CONTEXT_PRINCIPLE 5: Integration and compatibility contexts help identify intent when multiple technologies are mentioned together."""
        
    def _generate_consolidated_principles(self):
        return """CONSOLIDATED_PRINCIPLE 1: Direct technology name mentions are the strongest indicators of intent, with exact matches providing highest confidence.

CONSOLIDATED_PRINCIPLE 2: Technical terminology and domain-specific vocabulary create reliable semantic clusters for intent classification.

CONSOLIDATED_PRINCIPLE 3: Error patterns, file extensions, and API references provide distinctive technical fingerprints for each intent category.

CONSOLIDATED_PRINCIPLE 4: Question structure and problem-solving context reveal the underlying intent beyond just technology identification.

CONSOLIDATED_PRINCIPLE 5: Integration contexts and multi-technology scenarios require analysis of primary vs. secondary technology mentions."""
        
    def _generate_classification(self):
        """Generate random but plausible classification"""
        labels = ["wordpress", "oracle", "svn", "apache", "excel", "matlab",
                 "visual-studio", "cocoa", "osx", "bash", "spring", "hibernate",
                 "scala", "sharepoint", "ajax", "qt", "drupal", "linq", "haskell", "magento"]
        return random.choice(labels)


def mock_student_learning(data_processor, mock_llm):
    """Simulate student agents learning principles"""
    
    logger.info("Simulating 4 student agents learning principles...")
    
    # Get demonstration examples
    examples = data_processor.demonstration_examples
    
    student_principles = []
    strategies = ["analytical", "pattern_based", "linguistic", "contextual"]
    
    for i, strategy in enumerate(strategies):
        logger.info(f"Student Agent {i+1} ({strategy}) learning...")
        
        # Simulate learning time
        time.sleep(1)
        
        # Generate mock principles
        if strategy == "analytical":
            response = mock_llm._generate_analytical_principles()
        elif strategy == "pattern_based":
            response = mock_llm._generate_pattern_principles()
        elif strategy == "linguistic":
            response = mock_llm._generate_linguistic_principles()
        else:
            response = mock_llm._generate_contextual_principles()
            
        # Parse principles
        principles = []
        lines = response.split('\n')
        for line in lines:
            if line.strip() and ('PRINCIPLE' in line or 'PATTERN' in line or 'LINGUISTIC_FEATURE' in line or 'CONTEXT_PRINCIPLE' in line):
                content = line.split(':', 1)[1].strip() if ':' in line else line.strip()
                principle = Principle(
                    id=f"student_{i+1}_principle_{len(principles)+1}",
                    content=content,
                    source_agent=f"student_agent_{i+1}",
                    confidence=random.uniform(0.7, 0.9)
                )
                principles.append(principle)
                
        student_principles.append(principles)
        logger.info(f"  Generated {len(principles)} principles")
        
    return student_principles


def mock_teacher_evaluation(student_principles, mock_llm):
    """Simulate teacher agent evaluation and consolidation"""
    
    logger.info("Teacher agent evaluating and consolidating principles...")
    
    # Simulate evaluation
    all_principles = []
    for principle_set in student_principles:
        all_principles.extend(principle_set)
        
    # Mock evaluation scores
    qualified_principles = []
    for principle in all_principles:
        adequacy = random.uniform(0.6, 0.9)
        clarity = random.uniform(0.6, 0.9)
        overall_score = (adequacy + clarity) / 2
        
        principle.evaluation_scores = {
            'adequacy': adequacy,
            'clarity': clarity
        }
        
        if overall_score >= 0.7:  # Quality threshold
            qualified_principles.append(principle)
            
    logger.info(f"  {len(qualified_principles)} principles passed quality threshold")
    
    # Generate consolidated principles
    time.sleep(2)  # Simulate consolidation time
    
    response = mock_llm._generate_consolidated_principles()
    consolidated_principles = []
    
    lines = response.split('\n')
    for line in lines:
        if line.strip() and 'CONSOLIDATED_PRINCIPLE' in line:
            content = line.split(':', 1)[1].strip() if ':' in line else line.strip()
            principle = Principle(
                id=f"teacher_consolidated_{len(consolidated_principles)+1}",
                content=content,
                source_agent="teacher_agent",
                confidence=1.0
            )
            consolidated_principles.append(principle)
            
    logger.info(f"  Generated {len(consolidated_principles)} consolidated principles")
    return consolidated_principles


def mock_exam_classification(test_data, principles, mock_llm):
    """Simulate exam agent classification"""
    
    logger.info(f"Exam agent classifying {len(test_data)} test samples...")
    
    predictions = []
    true_labels = []
    
    # Use only first 100 samples for demo
    test_samples = test_data[:100]
    
    for i, example in enumerate(test_samples):
        if i % 20 == 0:
            logger.info(f"  Processed {i}/{len(test_samples)} samples")
            
        # Simulate classification time
        if i % 10 == 0:
            time.sleep(0.5)
            
        # Generate prediction (with some accuracy simulation)
        if random.random() < 0.75:  # 75% accuracy simulation
            prediction = example.label  # Correct prediction
        else:
            # Random incorrect prediction
            labels = ["wordpress", "oracle", "svn", "apache", "excel", "matlab",
                     "visual-studio", "cocoa", "osx", "bash", "spring", "hibernate",
                     "scala", "sharepoint", "ajax", "qt", "drupal", "linq", "haskell", "magento"]
            prediction = random.choice([l for l in labels if l != example.label])
            
        predictions.append(prediction)
        true_labels.append(example.label)
        
    logger.info(f"  Classification completed")
    return predictions, true_labels


def run_mock_demo():
    """Run the complete mock demo"""
    
    logger.info("="*60)
    logger.info("Multi-Agent Intent Detection Framework - MOCK DEMO")
    logger.info("Simulating LLM responses to demonstrate framework workflow")
    logger.info("="*60)
    
    try:
        # Load configuration and data
        config = load_config("config_demo.yaml")
        logger.info("Configuration loaded")
        
        # Create data processor
        data_config = {
            'train_path': config.data.train_path,
            'test_path': config.data.test_path,
            'dev_path': config.data.dev_path,
            'intent_labels': config.data.intent_labels,
            'examples_per_intent': config.data.examples_per_intent,
            'max_text_length': config.data.max_text_length,
            'demo_test_samples': 100
        }
        
        data_processor = create_data_processor(data_config)
        logger.info(f"Data loaded: {len(data_processor.train_data)} train, {len(data_processor.test_data)} test")
        
        # Initialize mock LLM
        mock_llm = MockLLMClient()
        
        # Phase 1: Student Learning
        logger.info("\n" + "="*60)
        logger.info("PHASE 1: STUDENT LEARNING")
        logger.info("="*60)
        
        start_time = time.time()
        student_principles = mock_student_learning(data_processor, mock_llm)
        learning_time = time.time() - start_time
        
        logger.info(f"Learning phase completed in {learning_time:.2f} seconds")
        
        # Phase 2: Teacher Evaluation
        logger.info("\n" + "="*60)
        logger.info("PHASE 2: TEACHER EVALUATION")
        logger.info("="*60)
        
        start_time = time.time()
        consolidated_principles = mock_teacher_evaluation(student_principles, mock_llm)
        evaluation_time = time.time() - start_time
        
        logger.info(f"Evaluation phase completed in {evaluation_time:.2f} seconds")
        
        # Show consolidated principles
        logger.info("\nConsolidated Principles:")
        for i, principle in enumerate(consolidated_principles):
            logger.info(f"{i+1}. {principle.content}")
            
        # Phase 3: Exam Classification
        logger.info("\n" + "="*60)
        logger.info("PHASE 3: EXAM CLASSIFICATION")
        logger.info("="*60)
        
        start_time = time.time()
        predictions, true_labels = mock_exam_classification(
            data_processor.test_data, consolidated_principles, mock_llm
        )
        classification_time = time.time() - start_time
        
        logger.info(f"Classification phase completed in {classification_time:.2f} seconds")
        
        # Evaluate results
        logger.info("\n" + "="*60)
        logger.info("EVALUATION RESULTS")
        logger.info("="*60)
        
        analyzer = ResultsAnalyzer(config.data.intent_labels, "demo_results")
        results = analyzer.analyze_results(
            y_true=true_labels,
            y_pred=predictions,
            experiment_name="mock_demo"
        )
        
        # Show summary
        metrics = results['overall_metrics']
        logger.info("="*60)
        logger.info("FINAL RESULTS SUMMARY")
        logger.info("="*60)
        logger.info(f"Test samples:  {len(true_labels)}")
        logger.info(f"Accuracy:      {metrics['accuracy']:.4f}")
        logger.info(f"F1 Macro:      {metrics['f1_macro']:.4f}")
        logger.info(f"F1 Micro:      {metrics['f1_micro']:.4f}")
        logger.info(f"Precision:     {metrics['precision_macro']:.4f}")
        logger.info(f"Recall:        {metrics['recall_macro']:.4f}")
        logger.info(f"LLM calls:     {mock_llm.call_count}")
        logger.info("="*60)
        
        logger.info("🎉 Mock demo completed successfully!")
        logger.info("\nThis demo simulated the complete multi-agent workflow:")
        logger.info("1. ✓ 4 Student agents learned principles using different strategies")
        logger.info("2. ✓ Teacher agent evaluated and consolidated principles")
        logger.info("3. ✓ Exam agent classified 100 test samples")
        logger.info("4. ✓ Results were evaluated with comprehensive metrics")
        
        return True
        
    except Exception as e:
        logger.error(f"Mock demo failed: {e}")
        logger.exception("Full traceback:")
        return False


def main():
    """Main function"""
    
    # Setup logging
    logger.remove()
    logger.add(sys.stdout, level="INFO", 
               format="{time:HH:mm:ss} | {level} | {message}")
    
    success = run_mock_demo()
    
    if success:
        logger.info("\n🎉 Demo completed successfully!")
        logger.info("\nTo run with actual LLM (requires model download):")
        logger.info("  conda activate LR && python demo_china.py --learning-only")
    else:
        logger.error("\n❌ Demo failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
