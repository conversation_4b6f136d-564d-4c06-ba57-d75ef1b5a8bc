#!/usr/bin/env python3
"""
调试vLLM内存问题的脚本
"""
import os
import torch
import psutil
from loguru import logger


def check_system_memory():
    """检查系统内存状态"""
    logger.info("=== 系统内存检查 ===")
    
    # CPU内存
    memory = psutil.virtual_memory()
    logger.info(f"系统总内存: {memory.total / 1024**3:.1f} GB")
    logger.info(f"可用内存: {memory.available / 1024**3:.1f} GB")
    logger.info(f"已用内存: {memory.used / 1024**3:.1f} GB ({memory.percent:.1f}%)")
    
    # GPU内存
    if torch.cuda.is_available():
        logger.info("\n=== GPU内存检查 ===")
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            logger.info(f"GPU {i}: {props.name}")
            logger.info(f"  总显存: {props.total_memory / 1024**3:.1f} GB")
            
            # 当前显存使用
            torch.cuda.set_device(i)
            allocated = torch.cuda.memory_allocated(i) / 1024**3
            reserved = torch.cuda.memory_reserved(i) / 1024**3
            logger.info(f"  已分配: {allocated:.1f} GB")
            logger.info(f"  已保留: {reserved:.1f} GB")
            logger.info(f"  可用: {(props.total_memory / 1024**3) - reserved:.1f} GB")
    else:
        logger.error("CUDA不可用")


def estimate_model_memory():
    """估算模型内存需求"""
    logger.info("\n=== 模型内存估算 ===")
    
    # Qwen2.5-7B参数量
    params_7b = 7.0e9  # 70亿参数
    
    # 不同精度的内存需求
    memory_fp32 = params_7b * 4 / 1024**3  # 32位浮点
    memory_fp16 = params_7b * 2 / 1024**3  # 16位浮点
    memory_int8 = params_7b * 1 / 1024**3  # 8位整数
    memory_int4 = params_7b * 0.5 / 1024**3  # 4位整数
    
    logger.info(f"FP32精度: {memory_fp32:.1f} GB")
    logger.info(f"FP16精度: {memory_fp16:.1f} GB")
    logger.info(f"INT8量化: {memory_int8:.1f} GB")
    logger.info(f"INT4量化: {memory_int4:.1f} GB")
    
    # vLLM额外开销
    logger.info("\nvLLM额外内存开销:")
    logger.info("- KV缓存: 2-4 GB (取决于序列长度)")
    logger.info("- 激活值: 1-2 GB")
    logger.info("- 其他开销: 1-2 GB")
    logger.info("总计额外开销: 4-8 GB")
    
    logger.info(f"\nvLLM + Qwen2.5-7B预估总需求:")
    logger.info(f"FP16模式: {memory_fp16 + 6:.1f} GB")
    logger.info(f"INT8模式: {memory_int8 + 4:.1f} GB")


def check_vllm_config():
    """检查vLLM配置问题"""
    logger.info("\n=== vLLM配置检查 ===")
    
    # 检查环境变量
    cuda_devices = os.environ.get('CUDA_VISIBLE_DEVICES', 'all')
    logger.info(f"CUDA_VISIBLE_DEVICES: {cuda_devices}")
    
    # 检查常见配置问题
    logger.info("\n常见vLLM OOM原因:")
    logger.info("1. tensor_parallel_size设置错误")
    logger.info("2. gpu_memory_utilization设置过高")
    logger.info("3. max_model_len设置过大")
    logger.info("4. 多进程冲突")
    logger.info("5. KV缓存设置不当")


def suggest_vllm_config():
    """建议vLLM配置"""
    logger.info("\n=== 推荐vLLM配置 ===")
    
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        total_memory = sum(torch.cuda.get_device_properties(i).total_memory 
                          for i in range(gpu_count)) / 1024**3
        
        logger.info(f"检测到 {gpu_count} 个GPU，总显存 {total_memory:.1f} GB")
        
        if total_memory >= 20:
            logger.info("\n推荐配置 (充足显存):")
            logger.info("- tensor_parallel_size: 1 (单GPU)")
            logger.info("- gpu_memory_utilization: 0.8")
            logger.info("- dtype: 'half' (FP16)")
            logger.info("- max_model_len: 4096")
        else:
            logger.info("\n推荐配置 (显存受限):")
            logger.info("- tensor_parallel_size: 1")
            logger.info("- gpu_memory_utilization: 0.7")
            logger.info("- dtype: 'half'")
            logger.info("- max_model_len: 2048")
            logger.info("- 考虑使用量化模型")


def test_simple_vllm():
    """测试简单的vLLM配置"""
    logger.info("\n=== 测试简单vLLM配置 ===")
    
    try:
        from vllm import LLM, SamplingParams
        
        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        logger.info("尝试加载vLLM (保守配置)...")
        
        # 保守配置
        llm = LLM(
            model="Qwen/Qwen2.5-7B-Instruct",
            tensor_parallel_size=1,
            gpu_memory_utilization=0.7,
            dtype="half",
            max_model_len=2048,
            trust_remote_code=True
        )
        
        logger.success("vLLM加载成功!")
        
        # 测试生成
        sampling_params = SamplingParams(temperature=0.2, top_p=0.9, max_tokens=50)
        outputs = llm.generate(["Hello, how are you?"], sampling_params)
        
        for output in outputs:
            logger.info(f"生成结果: {output.outputs[0].text}")
        
        logger.success("vLLM测试成功!")
        return True
        
    except Exception as e:
        logger.error(f"vLLM测试失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("vLLM内存问题诊断工具")
    logger.info("="*50)
    
    # 1. 检查系统内存
    check_system_memory()
    
    # 2. 估算模型内存
    estimate_model_memory()
    
    # 3. 检查配置
    check_vllm_config()
    
    # 4. 建议配置
    suggest_vllm_config()
    
    # 5. 测试vLLM
    logger.info("\n是否要测试vLLM配置? (y/n)")
    choice = input().lower().strip()
    
    if choice == 'y':
        test_simple_vllm()
    
    logger.info("\n=== 诊断完成 ===")


if __name__ == "__main__":
    main()
