#!/bin/bash

# 使用中国大陆镜像安装Ollama

set -e

echo "=========================================="
echo "使用中国镜像安装Ollama"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检测系统架构
detect_arch() {
    local arch=$(uname -m)
    case $arch in
        x86_64)
            echo "amd64"
            ;;
        aarch64|arm64)
            echo "arm64"
            ;;
        *)
            log_error "不支持的架构: $arch"
            exit 1
            ;;
    esac
}

# 使用多个中国镜像源下载
download_ollama_china() {
    local arch=$(detect_arch)
    local version="v0.1.17"
    
    log_info "检测到架构: $arch"
    log_info "使用中国镜像下载Ollama..."
    
    # 创建临时目录
    local temp_dir=$(mktemp -d)
    cd "$temp_dir"
    
    # 中国镜像源列表
    local mirrors=(
        "https://ghproxy.com/https://github.com/ollama/ollama/releases/download/${version}/ollama-linux-${arch}"
        "https://mirror.ghproxy.com/https://github.com/ollama/ollama/releases/download/${version}/ollama-linux-${arch}"
        "https://gh.api.99988866.xyz/https://github.com/ollama/ollama/releases/download/${version}/ollama-linux-${arch}"
        "https://github.moeyy.xyz/https://github.com/ollama/ollama/releases/download/${version}/ollama-linux-${arch}"
        "https://hub.fastgit.xyz/ollama/ollama/releases/download/${version}/ollama-linux-${arch}"
    )
    
    # 尝试每个镜像源
    for mirror in "${mirrors[@]}"; do
        log_info "尝试镜像: $(echo $mirror | cut -d'/' -f3)"
        
        if command -v wget &> /dev/null; then
            if wget --timeout=30 --tries=2 -O ollama "$mirror"; then
                log_success "下载成功"
                break
            else
                log_error "wget下载失败，尝试下一个镜像"
                continue
            fi
        elif command -v curl &> /dev/null; then
            if curl --connect-timeout 30 --max-time 300 -L -o ollama "$mirror"; then
                log_success "下载成功"
                break
            else
                log_error "curl下载失败，尝试下一个镜像"
                continue
            fi
        fi
    done
    
    # 检查文件
    if [[ ! -f "ollama" ]] || [[ ! -s "ollama" ]]; then
        log_error "所有镜像下载失败"
        return 1
    fi
    
    # 验证文件
    if file ollama | grep -q "ELF.*executable"; then
        log_success "下载的文件验证通过"
    else
        log_error "下载的文件不是有效的可执行文件"
        return 1
    fi
    
    # 设置执行权限
    chmod +x ollama
    
    # 安装到用户目录（避免需要sudo）
    local install_dir="$HOME/.local/bin"
    mkdir -p "$install_dir"
    
    if mv ollama "$install_dir/"; then
        log_success "Ollama安装到 $install_dir/"
        
        # 添加到PATH
        if ! echo "$PATH" | grep -q "$install_dir"; then
            echo "export PATH=\"$install_dir:\$PATH\"" >> ~/.bashrc
            export PATH="$install_dir:$PATH"
            log_info "已添加到PATH，重新加载shell或运行: source ~/.bashrc"
        fi
    else
        log_error "安装失败"
        return 1
    fi
    
    # 清理临时文件
    cd - > /dev/null
    rm -rf "$temp_dir"
    
    return 0
}

# 验证安装
verify_installation() {
    log_info "验证Ollama安装..."
    
    # 重新加载PATH
    export PATH="$HOME/.local/bin:$PATH"
    
    if command -v ollama &> /dev/null; then
        local version=$(ollama --version 2>/dev/null || echo "unknown")
        log_success "Ollama安装成功: $version"
        log_info "安装位置: $(which ollama)"
        return 0
    else
        log_error "Ollama安装验证失败"
        log_info "请手动添加到PATH: export PATH=\"$HOME/.local/bin:\$PATH\""
        return 1
    fi
}

# 启动Ollama服务
start_service() {
    log_info "启动Ollama服务..."
    
    # 确保PATH正确
    export PATH="$HOME/.local/bin:$PATH"
    
    # 检查服务是否已运行
    if curl -s http://localhost:11434/api/tags &> /dev/null; then
        log_success "Ollama服务已在运行"
        return 0
    fi
    
    # 启动服务
    log_info "在后台启动Ollama服务..."
    nohup ollama serve > /tmp/ollama.log 2>&1 &
    local pid=$!
    
    log_info "等待服务启动 (PID: $pid)..."
    
    # 等待服务启动
    for i in {1..30}; do
        if curl -s http://localhost:11434/api/tags &> /dev/null; then
            log_success "Ollama服务启动成功"
            log_info "服务日志: tail -f /tmp/ollama.log"
            return 0
        fi
        sleep 1
        echo -n "."
    done
    
    echo ""
    log_error "服务启动超时"
    log_info "检查日志: tail -f /tmp/ollama.log"
    return 1
}

# 下载模型（使用中国镜像）
pull_model() {
    log_info "拉取Qwen2.5-7B模型..."
    
    # 确保PATH正确
    export PATH="$HOME/.local/bin:$PATH"
    
    # 设置Ollama使用中国镜像
    export OLLAMA_HOST="0.0.0.0:11434"
    
    # 尝试拉取模型
    if ollama pull qwen2.5:7b; then
        log_success "Qwen2.5-7B模型拉取成功"
        return 0
    else
        log_error "模型拉取失败，尝试量化版本..."
        if ollama pull qwen2.5:7b-q4_0; then
            log_success "Qwen2.5-7B量化模型拉取成功"
            return 0
        else
            log_error "模型拉取失败"
            log_info "可能的原因："
            log_info "1. 网络连接问题"
            log_info "2. 磁盘空间不足"
            log_info "3. 模型服务器问题"
            return 1
        fi
    fi
}

# 测试模型
test_model() {
    log_info "测试Qwen模型..."
    
    # 确保PATH正确
    export PATH="$HOME/.local/bin:$PATH"
    
    local response=$(timeout 30s ollama run qwen2.5:7b "你好" 2>/dev/null || echo "")
    
    if [[ -n "$response" ]]; then
        log_success "模型测试成功"
        echo "模型响应: $response"
        return 0
    else
        log_error "模型测试失败"
        return 1
    fi
}

# 显示使用说明
show_usage() {
    echo ""
    log_success "Ollama安装完成！"
    echo ""
    echo "使用说明："
    echo "1. 确保PATH包含: export PATH=\"$HOME/.local/bin:\$PATH\""
    echo "2. 启动服务: ollama serve"
    echo "3. 拉取模型: ollama pull qwen2.5:7b"
    echo "4. 运行模型: ollama run qwen2.5:7b"
    echo "5. 查看模型: ollama list"
    echo ""
    echo "API端点: http://localhost:11434"
    echo "服务日志: tail -f /tmp/ollama.log"
    echo ""
    echo "如果遇到问题，请检查："
    echo "- 网络连接"
    echo "- 磁盘空间"
    echo "- 防火墙设置"
    echo ""
}

# 主函数
main() {
    log_info "开始使用中国镜像安装Ollama..."
    
    # 检查是否已安装
    export PATH="$HOME/.local/bin:$PATH"
    if command -v ollama &> /dev/null; then
        log_success "Ollama已安装"
        ollama --version
    else
        # 下载并安装
        if ! download_ollama_china; then
            log_error "下载安装失败"
            exit 1
        fi
        
        # 验证安装
        if ! verify_installation; then
            log_error "安装验证失败"
            exit 1
        fi
    fi
    
    # 启动服务
    if ! start_service; then
        log_error "服务启动失败"
        exit 1
    fi
    
    # 拉取模型
    if ! pull_model; then
        log_error "模型拉取失败，但Ollama已安装"
        log_info "可以稍后手动拉取: ollama pull qwen2.5:7b"
    fi
    
    # 测试模型
    test_model
    
    # 显示使用说明
    show_usage
}

# 运行主函数
main "$@"
