#!/bin/bash

# 手动安装Ollama的脚本
# 解决网络问题和下载失败

set -e

echo "=========================================="
echo "手动安装Ollama"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检测系统架构
detect_arch() {
    local arch=$(uname -m)
    case $arch in
        x86_64)
            echo "amd64"
            ;;
        aarch64|arm64)
            echo "arm64"
            ;;
        *)
            log_error "不支持的架构: $arch"
            exit 1
            ;;
    esac
}

# 下载Ollama二进制文件
download_ollama() {
    local arch=$(detect_arch)
    local version="v0.1.17"  # 使用稳定版本
    local url="https://github.com/ollama/ollama/releases/download/${version}/ollama-linux-${arch}"
    
    log_info "检测到架构: $arch"
    log_info "下载Ollama二进制文件..."
    
    # 创建临时目录
    local temp_dir=$(mktemp -d)
    cd "$temp_dir"
    
    # 尝试多个下载方法
    if command -v wget &> /dev/null; then
        log_info "使用wget下载..."
        if wget -O ollama "$url"; then
            log_success "下载成功"
        else
            log_error "wget下载失败"
            return 1
        fi
    elif command -v curl &> /dev/null; then
        log_info "使用curl下载..."
        if curl -L -o ollama "$url"; then
            log_success "下载成功"
        else
            log_error "curl下载失败"
            return 1
        fi
    else
        log_error "未找到wget或curl"
        return 1
    fi
    
    # 检查文件
    if [[ ! -f "ollama" ]] || [[ ! -s "ollama" ]]; then
        log_error "下载的文件无效"
        return 1
    fi
    
    # 设置执行权限
    chmod +x ollama
    
    # 安装到系统目录
    log_info "安装Ollama到 /usr/local/bin/..."
    if sudo mv ollama /usr/local/bin/; then
        log_success "Ollama安装成功"
    else
        log_error "安装失败"
        return 1
    fi
    
    # 清理临时文件
    cd - > /dev/null
    rm -rf "$temp_dir"
    
    return 0
}

# 验证安装
verify_installation() {
    log_info "验证Ollama安装..."
    
    if command -v ollama &> /dev/null; then
        local version=$(ollama --version 2>/dev/null || echo "unknown")
        log_success "Ollama安装成功: $version"
        return 0
    else
        log_error "Ollama安装验证失败"
        return 1
    fi
}

# 启动Ollama服务
start_service() {
    log_info "启动Ollama服务..."
    
    # 检查服务是否已运行
    if curl -s http://localhost:11434/api/tags &> /dev/null; then
        log_success "Ollama服务已在运行"
        return 0
    fi
    
    # 启动服务
    nohup ollama serve > /tmp/ollama.log 2>&1 &
    local pid=$!
    
    log_info "等待服务启动 (PID: $pid)..."
    
    # 等待服务启动
    for i in {1..30}; do
        if curl -s http://localhost:11434/api/tags &> /dev/null; then
            log_success "Ollama服务启动成功"
            return 0
        fi
        sleep 1
        echo -n "."
    done
    
    echo ""
    log_error "服务启动超时"
    log_info "检查日志: tail -f /tmp/ollama.log"
    return 1
}

# 主函数
main() {
    log_info "开始手动安装Ollama..."
    
    # 检查是否已安装
    if command -v ollama &> /dev/null; then
        log_success "Ollama已安装"
        ollama --version
    else
        # 下载并安装
        if ! download_ollama; then
            log_error "下载安装失败"
            exit 1
        fi
        
        # 验证安装
        if ! verify_installation; then
            log_error "安装验证失败"
            exit 1
        fi
    fi
    
    # 启动服务
    if ! start_service; then
        log_error "服务启动失败"
        exit 1
    fi
    
    log_success "Ollama手动安装完成！"
    log_info "现在可以使用: ollama pull qwen2.5:7b"
}

# 运行主函数
main "$@"
