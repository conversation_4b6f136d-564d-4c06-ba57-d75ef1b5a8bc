"""
Main multi-agent intent detection framework orchestrator
"""
import json
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from loguru import logger

from .utils.config import Config, load_config
from .utils.logging_setup import setup_logging, log_execution_time
from .utils.data_processor import create_data_processor
from .utils.llm_client import initialize_llm_client
from .agents.student_agents import create_student_agents
from .agents.teacher_agent import MathematicalTeacherAgent
from .agents.exam_agent import ClassificationExamAgent, AdvancedExamAgent
from .agents.base_agent import Principle, IntentExample
from .evaluation.metrics import ResultsAnalyzer


class MultiAgentIntentDetectionFramework:
    """
    Main framework orchestrating the multi-agent intent detection system
    """
    
    def __init__(self, config_path: Optional[str] = None):
        # Load configuration
        self.config = load_config(config_path)
        
        # Setup logging
        setup_logging(self.config.logging)
        
        # Initialize components
        self.data_processor = None
        self.student_agents = []
        self.teacher_agent = None
        self.exam_agent = None
        self.results_analyzer = None
        
        # Results storage
        self.results = {}
        self.intermediate_results = {}
        
        logger.info("Multi-Agent Intent Detection Framework initialized")
        
    @log_execution_time
    def initialize(self) -> None:
        """Initialize all framework components"""
        
        logger.info("Initializing framework components...")
        
        # Initialize LLM client
        logger.info("Initializing LLM client...")
        initialize_llm_client(
            model_name=self.config.model.name,
            gpu_devices=self.config.model.gpu_devices,
            tensor_parallel_size=self.config.model.tensor_parallel_size,
            dtype=self.config.model.dtype,
            hf_mirror=self.config.model.hf_mirror
        )
        
        # Initialize data processor
        logger.info("Initializing data processor...")
        data_config = {
            'train_path': self.config.data.train_path,
            'test_path': self.config.data.test_path,
            'dev_path': self.config.data.dev_path,
            'intent_labels': self.config.data.intent_labels,
            'examples_per_intent': self.config.data.examples_per_intent,
            'max_text_length': self.config.data.max_text_length
        }
        self.data_processor = create_data_processor(data_config)
        
        # Initialize student agents
        logger.info("Initializing student agents...")
        student_config = {
            'strategies': self.config.agents['student'].strategies,
            'max_principles': self.config.agents['student'].max_principles,
            'temperature': self.config.model.temperature,
            'top_p': self.config.model.top_p,
            'max_tokens': self.config.model.max_tokens
        }
        self.student_agents = create_student_agents(student_config)
        
        # Initialize teacher agent
        logger.info("Initializing teacher agent...")
        teacher_config = {
            'evaluation_dimensions': self.config.agents['teacher'].evaluation_dimensions,
            'dimension_weights': self.config.agents['teacher'].dimension_weights,
            'quality_threshold': self.config.agents['teacher'].quality_threshold,
            'temperature': self.config.model.temperature,
            'top_p': self.config.model.top_p,
            'max_tokens': self.config.model.max_tokens
        }
        self.teacher_agent = MathematicalTeacherAgent("teacher_agent", teacher_config)
        
        # Initialize exam agent
        logger.info("Initializing exam agent...")
        exam_config = {
            'batch_size': self.config.agents['exam'].batch_size,
            'evaluation_metrics': self.config.agents['exam'].evaluation_metrics,
            'temperature': self.config.model.temperature,
            'top_p': self.config.model.top_p,
            'max_tokens': self.config.model.max_tokens
        }
        self.exam_agent = AdvancedExamAgent("exam_agent", exam_config)
        
        # Initialize results analyzer
        self.results_analyzer = ResultsAnalyzer(
            intent_labels=self.config.data.intent_labels,
            output_dir=self.config.framework.output_dir
        )
        
        logger.info("Framework initialization completed")
        
    @log_execution_time
    def run_learning_phase(self) -> List[Principle]:
        """
        Run the learning phase with student and teacher agents
        
        Returns:
            Consolidated principles from teacher agent
        """
        
        logger.info("Starting learning phase...")
        
        # Get demonstration examples
        demonstration_examples = self.data_processor.demonstration_examples
        task_description = "Intent detection for technical texts"
        
        # Phase 1: Student agents learn principles
        logger.info("Phase 1: Student agents learning principles...")
        student_principles = self._run_student_learning(task_description, demonstration_examples)
        
        # Save intermediate results
        if self.config.framework.save_intermediate_results:
            self._save_student_principles(student_principles)
            
        # Phase 2: Teacher agent evaluates and consolidates
        logger.info("Phase 2: Teacher agent evaluating and consolidating principles...")
        consolidated_principles = self._run_teacher_evaluation(
            student_principles, task_description, demonstration_examples
        )
        
        # Save consolidated principles
        if self.config.framework.save_intermediate_results:
            self._save_consolidated_principles(consolidated_principles)
            
        logger.info(f"Learning phase completed. Generated {len(consolidated_principles)} consolidated principles")
        return consolidated_principles
        
    def _run_student_learning(
        self,
        task_description: str,
        examples: List[IntentExample]
    ) -> List[List[Principle]]:
        """Run student agents to learn principles"""
        
        student_principles = []
        
        if self.config.framework.parallel_processing:
            # Parallel execution
            with ThreadPoolExecutor(max_workers=len(self.student_agents)) as executor:
                futures = []
                
                for agent in self.student_agents:
                    future = executor.submit(
                        agent.learn_principles,
                        task_description,
                        examples
                    )
                    futures.append((agent.agent_id, future))
                    
                for agent_id, future in futures:
                    try:
                        principles = future.result(timeout=300)  # 5 minute timeout
                        student_principles.append(principles)
                        logger.info(f"Agent {agent_id} learned {len(principles)} principles")
                    except Exception as e:
                        logger.error(f"Agent {agent_id} failed: {e}")
                        student_principles.append([])
        else:
            # Sequential execution
            for agent in self.student_agents:
                try:
                    principles = agent.learn_principles(task_description, examples)
                    student_principles.append(principles)
                    logger.info(f"Agent {agent.agent_id} learned {len(principles)} principles")
                except Exception as e:
                    logger.error(f"Agent {agent.agent_id} failed: {e}")
                    student_principles.append([])
                    
        return student_principles
        
    def _run_teacher_evaluation(
        self,
        student_principles: List[List[Principle]],
        task_description: str,
        examples: List[IntentExample]
    ) -> List[Principle]:
        """Run teacher agent to evaluate and consolidate principles"""
        
        try:
            consolidated_principles, evaluation_scores = self.teacher_agent.evaluate_principles(
                student_principles, task_description, examples
            )
            
            # Store evaluation results
            self.intermediate_results['teacher_evaluation'] = {
                'evaluation_scores': evaluation_scores,
                'num_input_principles': sum(len(ps) for ps in student_principles),
                'num_consolidated': len(consolidated_principles),
                'quality_threshold': self.config.agents['teacher'].quality_threshold
            }
            
            return consolidated_principles
            
        except Exception as e:
            logger.error(f"Teacher evaluation failed: {e}")
            # Fallback: return all student principles
            all_principles = []
            for principle_set in student_principles:
                all_principles.extend(principle_set)
            return all_principles
            
    @log_execution_time
    def run_testing_phase(self, principles: List[Principle]) -> Dict[str, Any]:
        """
        Run the testing phase with exam agent
        
        Args:
            principles: Consolidated principles from learning phase
            
        Returns:
            Evaluation results
        """
        
        logger.info("Starting testing phase...")
        
        # Get test data
        test_texts = [example.text for example in self.data_processor.test_data]
        test_labels = [example.label for example in self.data_processor.test_data]
        
        # Get demonstration examples for reference
        demonstration_examples = self.data_processor.demonstration_examples
        
        # Run classification
        logger.info(f"Classifying {len(test_texts)} test examples...")
        predictions = self.exam_agent.classify(
            texts=test_texts,
            principles=principles,
            intent_labels=self.config.data.intent_labels,
            examples=demonstration_examples
        )
        
        # Evaluate results
        logger.info("Evaluating classification results...")
        results = self.results_analyzer.analyze_results(
            y_true=test_labels,
            y_pred=predictions,
            texts=test_texts,
            experiment_name="multi_agent_intent_detection"
        )
        
        logger.info("Testing phase completed")
        return results
        
    @log_execution_time
    def run_full_pipeline(self) -> Dict[str, Any]:
        """
        Run the complete learning-testing pipeline
        
        Returns:
            Complete results including learning and testing phases
        """
        
        logger.info("Starting full multi-agent intent detection pipeline...")
        
        # Initialize framework
        self.initialize()
        
        # Learning phase
        consolidated_principles = self.run_learning_phase()
        
        # Testing phase
        testing_results = self.run_testing_phase(consolidated_principles)
        
        # Compile complete results
        complete_results = {
            'framework_config': self._config_to_dict(),
            'learning_phase': {
                'num_consolidated_principles': len(consolidated_principles),
                'consolidated_principles': [
                    {
                        'id': p.id,
                        'content': p.content,
                        'confidence': p.confidence,
                        'source_agent': p.source_agent
                    }
                    for p in consolidated_principles
                ],
                'intermediate_results': self.intermediate_results
            },
            'testing_phase': testing_results,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # Save complete results
        self._save_complete_results(complete_results)
        
        logger.info("Full pipeline completed successfully")
        return complete_results
        
    def _save_student_principles(self, student_principles: List[List[Principle]]) -> None:
        """Save student principles to file"""
        
        output_dir = Path(self.config.framework.output_dir)
        output_dir.mkdir(exist_ok=True)
        
        student_data = []
        for i, principle_set in enumerate(student_principles):
            agent_data = {
                'agent_id': f"student_agent_{i+1}",
                'strategy': self.student_agents[i].strategy if i < len(self.student_agents) else 'unknown',
                'principles': [
                    {
                        'id': p.id,
                        'content': p.content,
                        'confidence': p.confidence
                    }
                    for p in principle_set
                ]
            }
            student_data.append(agent_data)
            
        with open(output_dir / "student_principles.json", 'w') as f:
            json.dump(student_data, f, indent=2)
            
        logger.info("Student principles saved")
        
    def _save_consolidated_principles(self, principles: List[Principle]) -> None:
        """Save consolidated principles to file"""
        
        output_dir = Path(self.config.framework.output_dir)
        output_dir.mkdir(exist_ok=True)
        
        principles_data = [
            {
                'id': p.id,
                'content': p.content,
                'confidence': p.confidence,
                'source_agent': p.source_agent,
                'evaluation_scores': p.evaluation_scores
            }
            for p in principles
        ]
        
        with open(output_dir / "consolidated_principles.json", 'w') as f:
            json.dump(principles_data, f, indent=2)
            
        logger.info("Consolidated principles saved")
        
    def _save_complete_results(self, results: Dict[str, Any]) -> None:
        """Save complete results to file"""
        
        output_dir = Path(self.config.framework.output_dir)
        output_dir.mkdir(exist_ok=True)
        
        with open(output_dir / "complete_results.json", 'w') as f:
            json.dump(results, f, indent=2)
            
        logger.info("Complete results saved")
        
    def _config_to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary for serialization"""
        
        return {
            'model': {
                'name': self.config.model.name,
                'temperature': self.config.model.temperature,
                'top_p': self.config.model.top_p,
                'gpu_devices': self.config.model.gpu_devices
            },
            'data': {
                'intent_labels': self.config.data.intent_labels,
                'examples_per_intent': self.config.data.examples_per_intent
            },
            'agents': {
                'student_count': self.config.agents['student'].count,
                'student_strategies': self.config.agents['student'].strategies,
                'teacher_threshold': self.config.agents['teacher'].quality_threshold,
                'exam_batch_size': self.config.agents['exam'].batch_size
            }
        }
