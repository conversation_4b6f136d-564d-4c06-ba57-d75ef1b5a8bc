"""
LLM Client for vLLM integration with GPU management
"""
import os
import asyncio
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass
import torch
from vllm import LLM, SamplingParams
from loguru import logger


@dataclass
class GenerationConfig:
    """Configuration for text generation"""
    temperature: float = 0.2
    top_p: float = 0.9
    max_tokens: int = 2048
    stop: Optional[List[str]] = None
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0


class VLLMClient:
    """
    vLLM client with GPU management for distributed inference
    """
    
    def __init__(
        self,
        model_name: str,
        gpu_devices: List[int] = [0, 1],
        tensor_parallel_size: int = 2,
        dtype: str = "half",
        hf_mirror: Optional[str] = None
    ):
        self.model_name = model_name
        self.gpu_devices = gpu_devices
        self.tensor_parallel_size = tensor_parallel_size
        self.dtype = dtype
        self.hf_mirror = hf_mirror
        self.llm = None
        self._setup_environment()
        
    def _setup_environment(self):
        """Setup environment for vLLM and HuggingFace"""
        # Set CUDA devices
        os.environ["CUDA_VISIBLE_DEVICES"] = ",".join(map(str, self.gpu_devices))

        # Set HuggingFace mirror if provided
        if self.hf_mirror:
            os.environ["HF_ENDPOINT"] = self.hf_mirror
            os.environ["HUGGINGFACE_HUB_CACHE"] = os.path.expanduser("~/.cache/huggingface")
            # Additional environment variables for China mirror
            os.environ["HF_HUB_OFFLINE"] = "0"
            os.environ["TRANSFORMERS_OFFLINE"] = "0"
            # 避免访问huggingface.co的额外请求
            os.environ["HF_HUB_DISABLE_TELEMETRY"] = "1"
            os.environ["TOKENIZERS_PARALLELISM"] = "false"
            logger.info(f"Using HuggingFace mirror: {self.hf_mirror}")

        logger.info(f"Using GPUs: {self.gpu_devices}")
        logger.info(f"Tensor parallel size: {self.tensor_parallel_size}")
        
    def initialize_model(self):
        """Initialize the vLLM model"""
        try:
            logger.info(f"Loading model: {self.model_name}")

            # 清理GPU内存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                logger.info("Cleared GPU cache")

            # 保守的内存配置
            gpu_memory_util = 0.8 if self.tensor_parallel_size == 1 else 0.7
            max_model_len = 2048  # 减少序列长度以节省KV缓存内存

            logger.info(f"vLLM配置:")
            logger.info(f"  - tensor_parallel_size: {self.tensor_parallel_size}")
            logger.info(f"  - gpu_memory_utilization: {gpu_memory_util}")
            logger.info(f"  - dtype: {self.dtype}")
            logger.info(f"  - max_model_len: {max_model_len}")

            self.llm = LLM(
                model=self.model_name,
                tensor_parallel_size=self.tensor_parallel_size,
                dtype=self.dtype,
                gpu_memory_utilization=gpu_memory_util,
                max_model_len=max_model_len,
                trust_remote_code=True,
                enforce_eager=True,  # 避免CUDA图内存问题
                disable_custom_all_reduce=True,  # 避免多GPU通信问题
                download_dir=os.environ.get("HUGGINGFACE_HUB_CACHE"),  # 使用缓存目录
                skip_tokenizer_init=False,  # 确保tokenizer正确初始化
                tokenizer_mode="auto"  # 自动选择tokenizer模式
            )
            logger.info("Model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            logger.info("尝试更保守的配置...")

            # 更保守的配置
            try:
                self.llm = LLM(
                    model=self.model_name,
                    tensor_parallel_size=1,  # 强制单GPU
                    dtype="half",
                    gpu_memory_utilization=0.6,  # 更低的内存使用
                    max_model_len=1024,  # 更短的序列
                    trust_remote_code=True,
                    enforce_eager=True
                )
                logger.info("Model loaded with conservative config")
            except Exception as e2:
                logger.error(f"Conservative config also failed: {e2}")
                raise
            
    def generate(
        self,
        prompts: Union[str, List[str]],
        config: Optional[GenerationConfig] = None
    ) -> Union[str, List[str]]:
        """
        Generate text using vLLM
        
        Args:
            prompts: Single prompt or list of prompts
            config: Generation configuration
            
        Returns:
            Generated text(s)
        """
        if self.llm is None:
            raise RuntimeError("Model not initialized. Call initialize_model() first.")
            
        if config is None:
            config = GenerationConfig()
            
        # Convert single prompt to list
        single_prompt = isinstance(prompts, str)
        if single_prompt:
            prompts = [prompts]
            
        # Create sampling parameters
        sampling_params = SamplingParams(
            temperature=config.temperature,
            top_p=config.top_p,
            max_tokens=config.max_tokens,
            stop=config.stop,
            frequency_penalty=config.frequency_penalty,
            presence_penalty=config.presence_penalty
        )
        
        try:
            # Generate
            outputs = self.llm.generate(prompts, sampling_params)
            
            # Extract generated text
            results = []
            for output in outputs:
                generated_text = output.outputs[0].text.strip()
                results.append(generated_text)
                
            # Return single string if input was single prompt
            if single_prompt:
                return results[0]
            return results
            
        except Exception as e:
            logger.error(f"Generation failed: {e}")
            raise
            
    def batch_generate(
        self,
        prompts: List[str],
        config: Optional[GenerationConfig] = None,
        batch_size: int = 32
    ) -> List[str]:
        """
        Generate text in batches for large datasets
        
        Args:
            prompts: List of prompts
            config: Generation configuration
            batch_size: Batch size for processing
            
        Returns:
            List of generated texts
        """
        results = []
        
        for i in range(0, len(prompts), batch_size):
            batch = prompts[i:i + batch_size]
            batch_results = self.generate(batch, config)
            if isinstance(batch_results, str):
                batch_results = [batch_results]
            results.extend(batch_results)
            
            logger.info(f"Processed batch {i//batch_size + 1}/{(len(prompts)-1)//batch_size + 1}")
            
        return results
        
    def __del__(self):
        """Cleanup resources"""
        if hasattr(self, 'llm') and self.llm is not None:
            del self.llm
            torch.cuda.empty_cache()


# Global LLM client instance
_llm_client: Optional[VLLMClient] = None


def get_llm_client() -> VLLMClient:
    """Get the global LLM client instance"""
    global _llm_client
    if _llm_client is None:
        raise RuntimeError("LLM client not initialized. Call initialize_llm_client() first.")
    return _llm_client


def initialize_llm_client(
    model_name: str,
    gpu_devices: List[int] = [0, 1],
    tensor_parallel_size: int = 2,
    dtype: str = "half",
    hf_mirror: Optional[str] = None
) -> VLLMClient:
    """Initialize the global LLM client"""
    global _llm_client
    _llm_client = VLLMClient(
        model_name=model_name,
        gpu_devices=gpu_devices,
        tensor_parallel_size=tensor_parallel_size,
        dtype=dtype,
        hf_mirror=hf_mirror
    )
    _llm_client.initialize_model()
    return _llm_client
