#!/usr/bin/env python3
"""
重启多智能体框架，使用更新的配置
"""
import os
import sys
import time
import torch
from pathlib import Path
from loguru import logger

# 设置中国大陆镜像环境变量
def setup_china_mirrors():
    """设置中国大陆镜像"""
    mirror_env = {
        "HF_ENDPOINT": "https://hf-mirror.com",
        "HUGGINGFACE_HUB_CACHE": os.path.expanduser("~/.cache/huggingface"),
        "HF_HUB_OFFLINE": "0",
        "TRANSFORMERS_OFFLINE": "0", 
        "HF_HUB_DISABLE_TELEMETRY": "1",
        "TOKENIZERS_PARALLELISM": "false",
        "CUDA_VISIBLE_DEVICES": "0"  # 使用单GPU
    }
    
    for key, value in mirror_env.items():
        os.environ[key] = value

# 设置镜像
setup_china_mirrors()

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.framework import MultiAgentIntentDetectionFramework


def clear_gpu_memory():
    """清理GPU内存"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        logger.info("GPU内存已清理")


def restart_framework():
    """重启框架"""
    
    logger.info("="*80)
    logger.info("重启多智能体意图检测框架")
    logger.info("使用更新的配置 (max_model_len: 4096)")
    logger.info("="*80)
    
    try:
        # 1. 清理GPU内存
        clear_gpu_memory()
        
        # 2. 初始化框架
        logger.info("使用更新配置初始化框架...")
        framework = MultiAgentIntentDetectionFramework("config_demo.yaml")
        
        # 3. 运行完整流程
        logger.info("开始运行完整的学习-测试流程...")
        start_time = time.time()
        
        results = framework.run_full_pipeline()
        
        total_time = time.time() - start_time
        logger.info(f"完整流程耗时: {total_time:.2f} 秒 ({total_time/60:.1f} 分钟)")
        
        # 4. 显示结果
        if 'testing_phase' in results and 'overall_metrics' in results['testing_phase']:
            metrics = results['testing_phase']['overall_metrics']
            logger.info("\n🎯 最终结果:")
            logger.info(f"准确率: {metrics.get('accuracy', 0):.4f}")
            logger.info(f"F1分数 (Macro): {metrics.get('f1_macro', 0):.4f}")
            logger.info(f"F1分数 (Weighted): {metrics.get('f1_weighted', 0):.4f}")
            logger.info(f"精确率: {metrics.get('precision_macro', 0):.4f}")
            logger.info(f"召回率: {metrics.get('recall_macro', 0):.4f}")
        
        logger.info("🎉 框架重启并运行成功!")
        return True
        
    except Exception as e:
        logger.error(f"框架重启失败: {e}")
        logger.exception("详细错误:")
        return False


def main():
    """主函数"""
    
    # 设置日志
    logger.remove()
    logger.add(sys.stdout, level="INFO", 
               format="{time:HH:mm:ss} | {level} | {message}")
    
    logger.info("配置更新说明:")
    logger.info("- max_model_len: 2048 → 4096 (支持更长提示词)")
    logger.info("- gpu_memory_utilization: 0.8 → 0.75 (为更长序列预留内存)")
    logger.info("")
    
    success = restart_framework()
    
    if success:
        logger.info("\n🎉 重启成功!")
        logger.info("框架现在可以处理更长的提示词了")
    else:
        logger.error("\n❌ 重启失败!")
        logger.info("如果内存不足，可以进一步降低gpu_memory_utilization")
        sys.exit(1)


if __name__ == "__main__":
    main()
