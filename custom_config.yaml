agents:
  exam:
    batch_size: 16
    evaluation_metrics:
    - accuracy
    - f1_macro
    - f1_micro
    - precision
    - recall
  student:
    count: 4
    max_principles: 10
    strategies:
    - analytical
    - pattern_based
    - linguistic
    - contextual
  teacher:
    consolidation_strategy: weighted_synthesis
    dimension_weights:
    - 0.5
    - 0.5
    evaluation_dimensions: 2
    quality_threshold: 0.8
data:
  dev_path: stackoverflow/dev.tsv
  examples_per_intent: 1
  intent_labels:
  - wordpress
  - oracle
  - svn
  - apache
  - excel
  - matlab
  - visual-studio
  - cocoa
  - osx
  - bash
  - spring
  - hibernate
  - scala
  - sharepoint
  - ajax
  - qt
  - drupal
  - linq
  - haskell
  - magento
  max_text_length: 512
  test_path: stackoverflow/test.tsv
  train_path: stackoverflow/train.tsv
framework:
  convergence_threshold: 0.95
  max_iterations: 3
  output_dir: results
  parallel_processing: true
  save_intermediate_results: true
logging:
  file: logs/framework.log
  format: '{time:YYYY-MM-DD HH:mm:ss} | {level} | {name} | {message}'
  level: INFO
  retention: 7 days
  rotation: 1 day
model:
  dtype: half
  gpu_devices:
  - 0
  - 1
  hf_mirror: https://hf-mirror.com
  max_tokens: 2048
  name: Qwen/Qwen2.5-7B-Instruct
  temperature: 0.1
  tensor_parallel_size: 2
  top_p: 0.9
