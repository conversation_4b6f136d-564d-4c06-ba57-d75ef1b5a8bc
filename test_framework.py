#!/usr/bin/env python3
"""
Test script for the Multi-Agent Intent Detection Framework
"""
import sys
from pathlib import Path
import torch

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Test that all modules can be imported"""
    print("Testing imports...")
    
    try:
        from src.utils.config import load_config
        from src.utils.data_processor import create_data_processor
        from src.agents.student_agents import create_student_agents
        from src.agents.teacher_agent import MathematicalTeacherAgent
        from src.agents.exam_agent import ClassificationExamAgent
        from src.evaluation.metrics import MetricsCalculator
        from src.framework import MultiAgentIntentDetectionFramework
        print("✓ All imports successful")
        return True
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False

def test_config():
    """Test configuration loading"""
    print("Testing configuration...")
    
    try:
        from src.utils.config import load_config
        config = load_config("config.yaml")
        
        # Check key configuration values
        assert config.model.name == "Qwen/Qwen2.5-7B-Instruct"
        assert len(config.data.intent_labels) == 20
        assert config.agents['student'].count == 4
        assert len(config.agents['student'].strategies) == 4
        
        print("✓ Configuration loading successful")
        return True
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_data_processor():
    """Test data processor"""
    print("Testing data processor...")
    
    try:
        from src.utils.config import load_config
        from src.utils.data_processor import create_data_processor
        
        config = load_config("config.yaml")
        data_config = {
            'train_path': config.data.train_path,
            'test_path': config.data.test_path,
            'dev_path': config.data.dev_path,
            'intent_labels': config.data.intent_labels,
            'examples_per_intent': config.data.examples_per_intent,
            'max_text_length': config.data.max_text_length
        }
        
        processor = create_data_processor(data_config)
        
        # Check data loading
        assert len(processor.train_data) > 0
        assert len(processor.test_data) > 0
        assert len(processor.demonstration_examples) > 0
        
        print(f"✓ Data processor successful")
        print(f"  - Train examples: {len(processor.train_data)}")
        print(f"  - Test examples: {len(processor.test_data)}")
        print(f"  - Demo examples: {len(processor.demonstration_examples)}")
        return True
    except Exception as e:
        print(f"✗ Data processor test failed: {e}")
        return False

def test_gpu_availability():
    """Test GPU availability"""
    print("Testing GPU availability...")
    
    if not torch.cuda.is_available():
        print("✗ CUDA not available")
        return False
        
    num_gpus = torch.cuda.device_count()
    print(f"✓ Found {num_gpus} GPU(s)")
    
    for i in range(min(num_gpus, 2)):  # Check first 2 GPUs
        gpu_name = torch.cuda.get_device_name(i)
        gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
        print(f"  - GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
        
    return True

def test_framework_initialization():
    """Test framework initialization (without LLM)"""
    print("Testing framework initialization...")
    
    try:
        from src.framework import MultiAgentIntentDetectionFramework
        
        # Create framework instance
        framework = MultiAgentIntentDetectionFramework("config.yaml")
        
        # Test configuration loading
        assert framework.config is not None
        assert len(framework.config.data.intent_labels) == 20
        
        print("✓ Framework initialization successful")
        return True
    except Exception as e:
        print(f"✗ Framework initialization failed: {e}")
        return False

def main():
    """Run all tests"""
    print("="*60)
    print("Multi-Agent Intent Detection Framework - Test Suite")
    print("="*60)
    
    tests = [
        test_imports,
        test_config,
        test_data_processor,
        test_gpu_availability,
        test_framework_initialization
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            print()
    
    print("="*60)
    print(f"Test Results: {passed}/{total} tests passed")
    print("="*60)
    
    if passed == total:
        print("🎉 All tests passed! Framework is ready to use.")
        print("\nTo run the full framework:")
        print("python main.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
