#!/bin/bash

# 中国大陆快速部署脚本

set -e

echo "=========================================="
echo "中国大陆快速部署 - 多智能体框架"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查网络连接
check_network() {
    log_info "检查网络连接..."
    
    # 测试GitHub连接
    if curl -s --connect-timeout 5 https://github.com &> /dev/null; then
        log_success "GitHub连接正常"
        return 0
    else
        log_error "GitHub连接失败"
        return 1
    fi
}

# 检查HuggingFace镜像连接
check_hf_mirror() {
    log_info "检查HuggingFace镜像连接..."
    
    if curl -s --connect-timeout 5 https://hf-mirror.com &> /dev/null; then
        log_success "HuggingFace镜像连接正常"
        return 0
    else
        log_error "HuggingFace镜像连接失败"
        return 1
    fi
}

# 设置中国镜像环境
setup_china_mirrors() {
    log_info "设置中国镜像环境..."
    
    # HuggingFace镜像
    export HF_ENDPOINT=https://hf-mirror.com
    export HUGGINGFACE_HUB_CACHE=$HOME/.cache/huggingface
    
    # pip镜像
    mkdir -p ~/.pip
    cat > ~/.pip/pip.conf << 'EOF'
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple
trusted-host = pypi.tuna.tsinghua.edu.cn
EOF
    
    log_success "中国镜像环境设置完成"
}

# 安装Python依赖
install_dependencies() {
    log_info "安装Python依赖..."
    
    # 基础依赖
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple \
        loguru pyyaml pandas numpy scikit-learn tqdm \
        matplotlib seaborn requests transformers torch
    
    log_success "Python依赖安装完成"
}

# 运行网络测试
run_network_test() {
    log_info "运行网络连接测试..."
    
    local github_ok=false
    local hf_ok=false
    
    if check_network; then
        github_ok=true
    fi
    
    if check_hf_mirror; then
        hf_ok=true
    fi
    
    echo ""
    log_info "网络测试结果："
    if $github_ok; then
        log_success "✓ GitHub可访问 - 可以使用Ollama"
    else
        log_error "✗ GitHub不可访问 - Ollama安装可能失败"
    fi
    
    if $hf_ok; then
        log_success "✓ HuggingFace镜像可访问 - 可以下载模型"
    else
        log_error "✗ HuggingFace镜像不可访问 - 将使用模拟模式"
    fi
    
    echo ""
    
    # 推荐方案
    if $github_ok && $hf_ok; then
        log_success "推荐方案: Ollama + 真实模型"
        return 0
    elif $hf_ok; then
        log_success "推荐方案: Transformers + 真实模型"
        return 1
    else
        log_success "推荐方案: 离线模拟模式"
        return 2
    fi
}

# 方案1: Ollama部署
deploy_ollama() {
    log_info "部署方案1: Ollama + Qwen2.5-7B"
    
    if ./install_ollama_china.sh; then
        log_success "Ollama部署成功"
        log_info "运行演示: python demo_ollama.py"
        return 0
    else
        log_error "Ollama部署失败"
        return 1
    fi
}

# 方案2: Transformers部署
deploy_transformers() {
    log_info "部署方案2: Transformers + Qwen2.5-7B"
    
    setup_china_mirrors
    install_dependencies
    
    log_info "运行Transformers演示..."
    if python demo_transformers.py; then
        log_success "Transformers演示成功"
        return 0
    else
        log_error "Transformers演示失败"
        return 1
    fi
}

# 方案3: 离线模拟
deploy_offline() {
    log_info "部署方案3: 离线模拟模式"
    
    setup_china_mirrors
    install_dependencies
    
    log_info "运行离线演示..."
    if python demo_offline.py; then
        log_success "离线演示成功"
        return 0
    else
        log_error "离线演示失败"
        return 1
    fi
}

# 主菜单
show_menu() {
    echo ""
    echo "请选择部署方案："
    echo "1) 自动选择最佳方案"
    echo "2) Ollama部署 (需要GitHub访问)"
    echo "3) Transformers部署 (需要HF镜像访问)"
    echo "4) 离线模拟模式 (无网络要求)"
    echo "5) 网络测试"
    echo "0) 退出"
    echo ""
}

# 自动选择方案
auto_deploy() {
    log_info "自动选择最佳部署方案..."
    
    run_network_test
    local result=$?
    
    case $result in
        0)
            log_info "选择方案: Ollama部署"
            deploy_ollama
            ;;
        1)
            log_info "选择方案: Transformers部署"
            deploy_transformers
            ;;
        2)
            log_info "选择方案: 离线模拟模式"
            deploy_offline
            ;;
    esac
}

# 主程序
main() {
    # 检查conda环境
    if [[ "$CONDA_DEFAULT_ENV" != "LR" ]]; then
        log_error "请先激活conda LR环境: conda activate LR"
        exit 1
    fi
    
    # 设置权限
    chmod +x install_ollama_china.sh demo_offline.py demo_transformers.py 2>/dev/null || true
    
    while true; do
        show_menu
        read -p "请输入选择 [0-5]: " choice
        
        case $choice in
            1)
                auto_deploy
                ;;
            2)
                deploy_ollama
                ;;
            3)
                deploy_transformers
                ;;
            4)
                deploy_offline
                ;;
            5)
                run_network_test
                ;;
            0)
                log_info "退出部署脚本"
                exit 0
                ;;
            *)
                log_error "无效选择，请重新输入"
                ;;
        esac
        
        echo ""
        read -p "按Enter键继续..."
    done
}

# 运行主程序
main "$@"
