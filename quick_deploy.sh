#!/bin/bash

# Qwen2.5-7B 快速部署脚本
# 支持多种部署方案

set -e

echo "=========================================="
echo "Qwen2.5-7B 多智能体框架快速部署"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if command -v $1 &> /dev/null; then
        return 0
    else
        return 1
    fi
}

# 检查conda环境
check_conda_env() {
    if [[ "$CONDA_DEFAULT_ENV" == "LR" ]]; then
        log_success "已在conda LR环境中"
        return 0
    else
        log_warning "请先激活conda LR环境: conda activate LR"
        return 1
    fi
}

# 安装Ollama
install_ollama() {
    log_info "检查Ollama安装状态..."
    
    if check_command ollama; then
        log_success "Ollama已安装"
        ollama --version
        return 0
    fi
    
    log_info "安装Ollama..."
    
    # 检查操作系统
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        curl -fsSL https://ollama.com/install.sh | sh
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        curl -fsSL https://ollama.com/install.sh | sh
    else
        log_error "不支持的操作系统: $OSTYPE"
        log_info "请手动安装Ollama: https://ollama.com/download"
        return 1
    fi
    
    if check_command ollama; then
        log_success "Ollama安装成功"
        return 0
    else
        log_error "Ollama安装失败"
        return 1
    fi
}

# 启动Ollama服务
start_ollama_service() {
    log_info "检查Ollama服务状态..."
    
    # 检查服务是否已运行
    if curl -s http://localhost:11434/api/tags &> /dev/null; then
        log_success "Ollama服务已运行"
        return 0
    fi
    
    log_info "启动Ollama服务..."
    
    # 后台启动服务
    nohup ollama serve > /dev/null 2>&1 &
    
    # 等待服务启动
    for i in {1..30}; do
        if curl -s http://localhost:11434/api/tags &> /dev/null; then
            log_success "Ollama服务启动成功"
            return 0
        fi
        sleep 1
    done
    
    log_error "Ollama服务启动超时"
    return 1
}

# 拉取Qwen模型
pull_qwen_model() {
    log_info "检查Qwen2.5-7B模型..."
    
    # 检查模型是否已存在
    if ollama list | grep -q "qwen2.5:7b"; then
        log_success "Qwen2.5-7B模型已存在"
        return 0
    fi
    
    log_info "拉取Qwen2.5-7B模型（这可能需要几分钟）..."
    
    # 拉取模型
    if ollama pull qwen2.5:7b; then
        log_success "Qwen2.5-7B模型拉取成功"
        return 0
    else
        log_error "模型拉取失败"
        log_info "尝试拉取量化版本..."
        if ollama pull qwen2.5:7b-q4_0; then
            log_success "Qwen2.5-7B量化模型拉取成功"
            return 0
        else
            log_error "模型拉取失败"
            return 1
        fi
    fi
}

# 测试模型
test_model() {
    log_info "测试Qwen2.5-7B模型..."
    
    # 测试模型响应
    response=$(ollama run qwen2.5:7b "你好" --timeout 30s 2>/dev/null || echo "")
    
    if [[ -n "$response" ]]; then
        log_success "模型测试成功"
        echo "模型响应: $response"
        return 0
    else
        log_error "模型测试失败"
        return 1
    fi
}

# 安装Python依赖
install_python_deps() {
    log_info "安装Python依赖..."
    
    if [[ -f "requirements.txt" ]]; then
        pip install -r requirements.txt
        log_success "Python依赖安装完成"
    else
        log_warning "requirements.txt不存在，手动安装关键依赖..."
        pip install loguru pyyaml pandas numpy scikit-learn tqdm matplotlib seaborn requests
        log_success "关键依赖安装完成"
    fi
}

# 运行测试
run_tests() {
    log_info "运行框架测试..."
    
    if [[ -f "test_framework.py" ]]; then
        python test_framework.py
        log_success "框架测试完成"
    else
        log_warning "test_framework.py不存在，跳过测试"
    fi
}

# 运行演示
run_demo() {
    log_info "运行Ollama演示..."
    
    if [[ -f "demo_ollama.py" ]]; then
        python demo_ollama.py
        log_success "演示运行完成"
    elif [[ -f "demo_mock.py" ]]; then
        log_info "运行模拟演示..."
        python demo_mock.py
        log_success "模拟演示完成"
    else
        log_warning "演示文件不存在"
    fi
}

# 主菜单
show_menu() {
    echo ""
    echo "请选择部署方案："
    echo "1) 完整部署 (Ollama + Qwen2.5-7B)"
    echo "2) 仅安装Ollama"
    echo "3) 仅拉取模型"
    echo "4) 运行测试"
    echo "5) 运行演示"
    echo "6) 检查状态"
    echo "0) 退出"
    echo ""
}

# 检查状态
check_status() {
    echo ""
    log_info "系统状态检查："
    
    # 检查conda环境
    if [[ "$CONDA_DEFAULT_ENV" == "LR" ]]; then
        log_success "✓ Conda LR环境已激活"
    else
        log_warning "✗ 未在conda LR环境中"
    fi
    
    # 检查Ollama
    if check_command ollama; then
        log_success "✓ Ollama已安装"
        ollama --version
    else
        log_warning "✗ Ollama未安装"
    fi
    
    # 检查服务
    if curl -s http://localhost:11434/api/tags &> /dev/null; then
        log_success "✓ Ollama服务运行中"
    else
        log_warning "✗ Ollama服务未运行"
    fi
    
    # 检查模型
    if check_command ollama && ollama list | grep -q "qwen2.5"; then
        log_success "✓ Qwen2.5模型已安装"
        ollama list | grep qwen2.5
    else
        log_warning "✗ Qwen2.5模型未安装"
    fi
    
    echo ""
}

# 完整部署
full_deploy() {
    log_info "开始完整部署..."
    
    # 检查conda环境
    if ! check_conda_env; then
        log_error "请先激活conda LR环境"
        return 1
    fi
    
    # 安装Ollama
    if ! install_ollama; then
        log_error "Ollama安装失败"
        return 1
    fi
    
    # 启动服务
    if ! start_ollama_service; then
        log_error "服务启动失败"
        return 1
    fi
    
    # 拉取模型
    if ! pull_qwen_model; then
        log_error "模型拉取失败"
        return 1
    fi
    
    # 测试模型
    if ! test_model; then
        log_warning "模型测试失败，但可能仍可使用"
    fi
    
    # 安装Python依赖
    install_python_deps
    
    # 运行测试
    run_tests
    
    log_success "完整部署完成！"
    log_info "现在可以运行演示: python demo_ollama.py"
}

# 主程序
main() {
    # 检查是否在正确目录
    if [[ ! -f "README.md" ]] && [[ ! -f "main.py" ]]; then
        log_warning "请在项目根目录运行此脚本"
    fi
    
    while true; do
        show_menu
        read -p "请输入选择 [0-6]: " choice
        
        case $choice in
            1)
                full_deploy
                ;;
            2)
                install_ollama
                start_ollama_service
                ;;
            3)
                pull_qwen_model
                test_model
                ;;
            4)
                run_tests
                ;;
            5)
                run_demo
                ;;
            6)
                check_status
                ;;
            0)
                log_info "退出部署脚本"
                exit 0
                ;;
            *)
                log_error "无效选择，请重新输入"
                ;;
        esac
        
        echo ""
        read -p "按Enter键继续..."
    done
}

# 运行主程序
main "$@"
