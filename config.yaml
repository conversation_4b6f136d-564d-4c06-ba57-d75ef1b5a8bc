# Multi-Agent Intent Detection Framework Configuration

# Model Configuration
model:
  name: "Qwen/Qwen2.5-7B-Instruct"
  hf_mirror: "https://hf-mirror.com"  # China mainland mirror
  temperature: 0.2
  top_p: 0.9
  max_tokens: 2048
  gpu_devices: [0, 1]  # GPU0 and GPU1
  tensor_parallel_size: 2
  dtype: "half"

# Data Configuration
data:
  train_path: "stackoverflow/train.tsv"
  test_path: "stackoverflow/test.tsv"
  dev_path: "stackoverflow/dev.tsv"
  intent_labels: [
    "wordpress", "oracle", "svn", "apache", "excel", "matlab",
    "visual-studio", "cocoa", "osx", "bash", "spring", "hibernate",
    "scala", "sharepoint", "ajax", "qt", "drupal", "linq", "haskell", "magento"
  ]
  examples_per_intent: 1  # Number of examples to show in prompts
  max_text_length: 512

# Agent Configuration
agents:
  student:
    count: 4
    strategies: ["analytical", "pattern_based", "linguistic", "contextual"]
    max_principles: 10
  
  teacher:
    evaluation_dimensions: 2
    dimension_weights: [0.5, 0.5]  # [adequacy, clarity]
    quality_threshold: 0.7
    consolidation_strategy: "weighted_synthesis"
  
  exam:
    batch_size: 32
    evaluation_metrics: ["accuracy", "f1_macro", "f1_micro", "precision", "recall"]

# Framework Configuration
framework:
  max_iterations: 3
  convergence_threshold: 0.95
  parallel_processing: true
  save_intermediate_results: true
  output_dir: "results"

# Logging Configuration
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name} | {message}"
  file: "logs/framework.log"
  rotation: "1 day"
  retention: "7 days"
