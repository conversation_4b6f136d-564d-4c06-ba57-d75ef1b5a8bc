#!/bin/bash

# 使用Docker部署Ollama和Qwen2.5-7B

set -e

echo "=========================================="
echo "Docker部署Ollama + Qwen2.5-7B"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker
check_docker() {
    if command -v docker &> /dev/null; then
        log_success "Docker已安装"
        docker --version
        return 0
    else
        log_error "Docker未安装"
        log_info "请先安装Docker: https://docs.docker.com/get-docker/"
        return 1
    fi
}

# 检查NVIDIA Docker支持
check_nvidia_docker() {
    if command -v nvidia-docker &> /dev/null || docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi &> /dev/null; then
        log_success "NVIDIA Docker支持已启用"
        return 0
    else
        log_error "NVIDIA Docker支持未启用"
        log_info "GPU加速将不可用"
        return 1
    fi
}

# 拉取Ollama Docker镜像
pull_ollama_image() {
    log_info "拉取Ollama Docker镜像..."
    
    if docker pull ollama/ollama:latest; then
        log_success "Ollama镜像拉取成功"
        return 0
    else
        log_error "镜像拉取失败"
        return 1
    fi
}

# 启动Ollama容器
start_ollama_container() {
    log_info "启动Ollama容器..."
    
    # 检查容器是否已存在
    if docker ps -a | grep -q "ollama-server"; then
        log_info "停止现有容器..."
        docker stop ollama-server 2>/dev/null || true
        docker rm ollama-server 2>/dev/null || true
    fi
    
    # 检查GPU支持
    local gpu_args=""
    if check_nvidia_docker; then
        gpu_args="--gpus all"
        log_info "启用GPU支持"
    fi
    
    # 启动容器
    if docker run -d \
        --name ollama-server \
        -p 11434:11434 \
        -v ollama-data:/root/.ollama \
        $gpu_args \
        ollama/ollama:latest; then
        log_success "Ollama容器启动成功"
    else
        log_error "容器启动失败"
        return 1
    fi
    
    # 等待服务启动
    log_info "等待Ollama服务启动..."
    for i in {1..30}; do
        if curl -s http://localhost:11434/api/tags &> /dev/null; then
            log_success "Ollama服务已就绪"
            return 0
        fi
        sleep 2
        echo -n "."
    done
    
    echo ""
    log_error "服务启动超时"
    return 1
}

# 拉取Qwen模型
pull_qwen_model() {
    log_info "拉取Qwen2.5-7B模型..."
    
    # 在容器中拉取模型
    if docker exec ollama-server ollama pull qwen2.5:7b; then
        log_success "Qwen2.5-7B模型拉取成功"
        return 0
    else
        log_error "模型拉取失败，尝试量化版本..."
        if docker exec ollama-server ollama pull qwen2.5:7b-q4_0; then
            log_success "Qwen2.5-7B量化模型拉取成功"
            return 0
        else
            log_error "模型拉取失败"
            return 1
        fi
    fi
}

# 测试模型
test_model() {
    log_info "测试Qwen模型..."
    
    local response=$(docker exec ollama-server ollama run qwen2.5:7b "你好，请简单介绍一下你自己" 2>/dev/null || echo "")
    
    if [[ -n "$response" ]]; then
        log_success "模型测试成功"
        echo "模型响应: $response"
        return 0
    else
        log_error "模型测试失败"
        return 1
    fi
}

# 创建Docker Compose文件
create_docker_compose() {
    log_info "创建Docker Compose配置..."
    
    cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  ollama:
    image: ollama/ollama:latest
    container_name: ollama-server
    ports:
      - "11434:11434"
    volumes:
      - ollama-data:/root/.ollama
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

volumes:
  ollama-data:
EOF
    
    log_success "Docker Compose文件已创建"
}

# 显示使用说明
show_usage() {
    echo ""
    log_success "Docker部署完成！"
    echo ""
    echo "使用说明："
    echo "1. 检查服务状态: docker ps"
    echo "2. 查看日志: docker logs ollama-server"
    echo "3. 进入容器: docker exec -it ollama-server bash"
    echo "4. 运行模型: docker exec ollama-server ollama run qwen2.5:7b"
    echo "5. 停止服务: docker stop ollama-server"
    echo "6. 启动服务: docker start ollama-server"
    echo ""
    echo "API端点: http://localhost:11434"
    echo ""
}

# 主函数
main() {
    log_info "开始Docker部署..."
    
    # 检查Docker
    if ! check_docker; then
        exit 1
    fi
    
    # 拉取镜像
    if ! pull_ollama_image; then
        exit 1
    fi
    
    # 启动容器
    if ! start_ollama_container; then
        exit 1
    fi
    
    # 拉取模型
    if ! pull_qwen_model; then
        log_error "模型拉取失败，但容器已启动"
        log_info "可以稍后手动拉取: docker exec ollama-server ollama pull qwen2.5:7b"
    fi
    
    # 测试模型
    test_model
    
    # 创建Docker Compose文件
    create_docker_compose
    
    # 显示使用说明
    show_usage
}

# 运行主函数
main "$@"
