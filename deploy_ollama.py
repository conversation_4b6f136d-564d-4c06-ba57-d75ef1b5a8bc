#!/usr/bin/env python3
"""
使用Ollama部署Qwen2.5-7B的方案
"""
import subprocess
import requests
import json
import time
from loguru import logger


def install_ollama():
    """安装Ollama"""
    try:
        # 检查是否已安装
        result = subprocess.run(['ollama', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            logger.info(f"Ollama已安装: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    logger.info("正在安装Ollama...")
    
    # 下载并安装Ollama
    install_cmd = "curl -fsSL https://ollama.com/install.sh | sh"
    result = subprocess.run(install_cmd, shell=True, capture_output=True, text=True)
    
    if result.returncode == 0:
        logger.info("Ollama安装成功")
        return True
    else:
        logger.error(f"Ollama安装失败: {result.stderr}")
        return False


def pull_qwen_model():
    """拉取Qwen2.5-7B模型"""
    logger.info("正在拉取Qwen2.5-7B模型...")
    
    # 使用qwen2.5:7b标签
    pull_cmd = ["ollama", "pull", "qwen2.5:7b"]
    
    try:
        process = subprocess.Popen(
            pull_cmd, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.STDOUT, 
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 实时显示下载进度
        for line in process.stdout:
            print(line.strip())
            
        process.wait()
        
        if process.returncode == 0:
            logger.info("Qwen2.5-7B模型拉取成功")
            return True
        else:
            logger.error("模型拉取失败")
            return False
            
    except Exception as e:
        logger.error(f"拉取模型时出错: {e}")
        return False


def start_ollama_server():
    """启动Ollama服务器"""
    logger.info("启动Ollama服务器...")
    
    try:
        # 检查服务是否已运行
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            logger.info("Ollama服务器已在运行")
            return True
    except:
        pass
    
    # 启动服务器
    try:
        subprocess.Popen(["ollama", "serve"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        # 等待服务器启动
        for i in range(30):
            try:
                response = requests.get("http://localhost:11434/api/tags", timeout=2)
                if response.status_code == 200:
                    logger.info("Ollama服务器启动成功")
                    return True
            except:
                time.sleep(1)
                
        logger.error("Ollama服务器启动超时")
        return False
        
    except Exception as e:
        logger.error(f"启动服务器失败: {e}")
        return False


def test_qwen_model():
    """测试Qwen模型"""
    logger.info("测试Qwen2.5-7B模型...")
    
    test_prompt = "你好，请介绍一下你自己。"
    
    payload = {
        "model": "qwen2.5:7b",
        "prompt": test_prompt,
        "stream": False
    }
    
    try:
        response = requests.post(
            "http://localhost:11434/api/generate",
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            logger.info("模型测试成功")
            logger.info(f"回复: {result['response']}")
            return True
        else:
            logger.error(f"模型测试失败: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"测试模型时出错: {e}")
        return False


class OllamaClient:
    """Ollama客户端类"""
    
    def __init__(self, model_name="qwen2.5:7b", base_url="http://localhost:11434"):
        self.model_name = model_name
        self.base_url = base_url
        
    def generate(self, prompt, temperature=0.2, top_p=0.9, max_tokens=2048):
        """生成文本"""
        payload = {
            "model": self.model_name,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": temperature,
                "top_p": top_p,
                "num_predict": max_tokens
            }
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['response']
            else:
                raise Exception(f"API调用失败: {response.status_code}")
                
        except Exception as e:
            logger.error(f"生成文本失败: {e}")
            raise
    
    def batch_generate(self, prompts, **kwargs):
        """批量生成"""
        results = []
        for prompt in prompts:
            result = self.generate(prompt, **kwargs)
            results.append(result)
        return results


def main():
    """主函数"""
    logger.info("=== Qwen2.5-7B Ollama部署方案 ===")
    
    # 1. 安装Ollama
    if not install_ollama():
        return False
    
    # 2. 启动服务器
    if not start_ollama_server():
        return False
    
    # 3. 拉取模型
    if not pull_qwen_model():
        return False
    
    # 4. 测试模型
    if not test_qwen_model():
        return False
    
    logger.info("=== 部署完成 ===")
    logger.info("使用方法:")
    logger.info("from deploy_ollama import OllamaClient")
    logger.info("client = OllamaClient()")
    logger.info("response = client.generate('你的问题')")
    
    return True


if __name__ == "__main__":
    main()
