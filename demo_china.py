#!/usr/bin/env python3
"""
Demo script for Multi-Agent Intent Detection Framework with China mirror
"""
import os
import sys
import time
from pathlib import Path
import torch
from loguru import logger

# Setup China mirror environment variables first
def setup_china_mirror():
    """Setup China mainland HuggingFace mirror"""
    env_vars = {
        "HF_ENDPOINT": "https://hf-mirror.com",
        "HUGGINGFACE_HUB_CACHE": os.path.expanduser("~/.cache/huggingface"),
        "HF_HUB_OFFLINE": "0",
        "TRANSFORMERS_OFFLINE": "0",
        "HF_HUB_DISABLE_TELEMETRY": "1",
        "TOKENIZERS_PARALLELISM": "false"
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
    
    logger.info("China mainland HuggingFace mirror configured")

# Setup mirror before importing other modules
setup_china_mirror()

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.framework import MultiAgentIntentDetectionFramework
from src.utils.config import load_config
from src.utils.logging_setup import setup_logging


def check_gpu_and_memory():
    """Check GPU availability and memory"""
    if not torch.cuda.is_available():
        logger.error("CUDA is not available. This framework requires GPU support.")
        return False
        
    num_gpus = torch.cuda.device_count()
    logger.info(f"Found {num_gpus} GPU(s)")
    
    for i in range(min(num_gpus, 2)):  # Check first 2 GPUs
        gpu_name = torch.cuda.get_device_name(i)
        gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
        logger.info(f"GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
        
        # Clear GPU memory
        torch.cuda.empty_cache()
        
    return True


def run_learning_demo():
    """Run learning phase demo with actual LLM"""
    
    logger.info("="*60)
    logger.info("Multi-Agent Intent Detection Framework - LEARNING DEMO")
    logger.info("Using China mainland HuggingFace mirror")
    logger.info("="*60)
    
    try:
        # Check GPU
        if not check_gpu_and_memory():
            logger.error("GPU check failed. Exiting.")
            return False
            
        # Load demo configuration
        config = load_config("config_demo.yaml")
        logger.info("Demo configuration loaded")
        logger.info(f"Model: {config.model.name}")
        logger.info(f"GPU devices: {config.model.gpu_devices}")
        
        # Initialize framework
        logger.info("Initializing framework...")
        framework = MultiAgentIntentDetectionFramework("config_demo.yaml")
        
        # Run learning phase
        logger.info("Starting learning phase...")
        start_time = time.time()
        
        framework.initialize()
        principles = framework.run_learning_phase()
        
        learning_time = time.time() - start_time
        logger.info(f"Learning phase completed in {learning_time:.2f} seconds")
        logger.info(f"Generated {len(principles)} consolidated principles")
        
        # Show all principles
        logger.info("="*60)
        logger.info("LEARNED PRINCIPLES")
        logger.info("="*60)
        for i, principle in enumerate(principles):
            logger.info(f"{i+1}. {principle.content}")
            logger.info(f"   Source: {principle.source_agent}")
            if principle.evaluation_scores:
                logger.info(f"   Scores: {principle.evaluation_scores}")
            logger.info("")
            
        logger.info("="*60)
        logger.info("Learning demo completed successfully!")
        logger.info("="*60)
        return True
        
    except Exception as e:
        logger.error(f"Learning demo failed: {e}")
        logger.exception("Full traceback:")
        return False
        
    finally:
        # Clean up GPU memory
        if torch.cuda.is_available():
            torch.cuda.empty_cache()


def run_full_demo():
    """Run full demo with 100 test samples"""
    
    logger.info("="*60)
    logger.info("Multi-Agent Intent Detection Framework - FULL DEMO")
    logger.info("Using 100 test samples with actual LLM inference")
    logger.info("="*60)
    
    try:
        # Check GPU
        if not check_gpu_and_memory():
            logger.error("GPU check failed. Exiting.")
            return False
            
        # Load demo configuration
        config = load_config("config_demo.yaml")
        logger.info("Demo configuration loaded")
        logger.info(f"Model: {config.model.name}")
        logger.info(f"Test samples: {config.data.demo_test_samples}")
        
        # Initialize framework
        logger.info("Initializing framework...")
        framework = MultiAgentIntentDetectionFramework("config_demo.yaml")
        
        # Run full pipeline
        logger.info("Starting full pipeline...")
        start_time = time.time()
        
        results = framework.run_full_pipeline()
        
        total_time = time.time() - start_time
        logger.info(f"Full pipeline completed in {total_time:.2f} seconds")
        
        # Show results
        testing_results = results.get('testing_phase', {})
        if 'overall_metrics' in testing_results:
            metrics = testing_results['overall_metrics']
            logger.info("="*60)
            logger.info("FINAL RESULTS")
            logger.info("="*60)
            logger.info(f"Test samples: {testing_results['dataset_stats']['total_samples']}")
            logger.info(f"Accuracy:     {metrics['accuracy']:.4f}")
            logger.info(f"F1 Macro:     {metrics['f1_macro']:.4f}")
            logger.info(f"F1 Micro:     {metrics['f1_micro']:.4f}")
            logger.info(f"Precision:    {metrics['precision_macro']:.4f}")
            logger.info(f"Recall:       {metrics['recall_macro']:.4f}")
            logger.info("="*60)
            
            # Show per-class results
            if 'per_class_metrics' in testing_results:
                logger.info("Top 10 classes by F1 score:")
                per_class = testing_results['per_class_metrics']
                sorted_classes = sorted(per_class.items(), key=lambda x: x[1]['f1'], reverse=True)
                for class_name, class_metrics in sorted_classes[:10]:
                    logger.info(f"  {class_name}: {class_metrics['f1']:.4f}")
                    
        logger.info("Full demo completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Full demo failed: {e}")
        logger.exception("Full traceback:")
        return False
        
    finally:
        # Clean up GPU memory
        if torch.cuda.is_available():
            torch.cuda.empty_cache()


def main():
    """Main function"""
    
    import argparse
    
    parser = argparse.ArgumentParser(description="Demo for Multi-Agent Intent Detection Framework (China Mirror)")
    parser.add_argument("--learning-only", action="store_true", help="Run only learning phase")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    # Setup basic logging
    logger.remove()
    log_level = "DEBUG" if args.verbose else "INFO"
    logger.add(sys.stdout, level=log_level, 
               format="{time:HH:mm:ss} | {level} | {message}")
    
    logger.info("Using China mainland HuggingFace mirror: https://hf-mirror.com")
    
    if args.learning_only:
        success = run_learning_demo()
    else:
        success = run_full_demo()
        
    if success:
        logger.info("🎉 Demo completed successfully!")
    else:
        logger.error("❌ Demo failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
