#!/usr/bin/env python3
"""
设置vLLM中国镜像环境
"""
import os
import sys
import subprocess
from loguru import logger


def setup_china_environment():
    """设置中国镜像环境"""
    logger.info("设置中国镜像环境...")
    
    # 环境变量
    env_vars = {
        "HF_ENDPOINT": "https://hf-mirror.com",
        "HUGGINGFACE_HUB_CACHE": os.path.expanduser("~/.cache/huggingface"),
        "HF_HUB_OFFLINE": "0",
        "TRANSFORMERS_OFFLINE": "0",
        "HF_HUB_DISABLE_TELEMETRY": "1",
        "TOKENIZERS_PARALLELISM": "false",
        "CUDA_VISIBLE_DEVICES": "0"
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
        logger.info(f"  {key} = {value}")
    
    # 创建缓存目录
    cache_dir = env_vars["HUGGINGFACE_HUB_CACHE"]
    os.makedirs(cache_dir, exist_ok=True)
    logger.info(f"缓存目录: {cache_dir}")
    
    # 设置pip镜像
    pip_conf_dir = os.path.expanduser("~/.pip")
    os.makedirs(pip_conf_dir, exist_ok=True)
    
    pip_conf_content = """[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple
trusted-host = pypi.tuna.tsinghua.edu.cn
"""
    
    with open(os.path.join(pip_conf_dir, "pip.conf"), "w") as f:
        f.write(pip_conf_content)
    
    logger.success("中国镜像环境设置完成")


def check_vllm_installation():
    """检查vLLM安装"""
    logger.info("检查vLLM安装...")
    
    try:
        import vllm
        logger.success(f"vLLM已安装: {vllm.__version__}")
        return True
    except ImportError:
        logger.warning("vLLM未安装")
        return False


def install_vllm():
    """安装vLLM"""
    logger.info("安装vLLM...")
    
    try:
        # 使用清华镜像安装
        cmd = [
            sys.executable, "-m", "pip", "install", 
            "-i", "https://pypi.tuna.tsinghua.edu.cn/simple",
            "vllm"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.success("vLLM安装成功")
            return True
        else:
            logger.error(f"vLLM安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"安装过程出错: {e}")
        return False


def test_vllm_basic():
    """测试vLLM基本功能"""
    logger.info("测试vLLM基本功能...")
    
    try:
        from vllm import LLM, SamplingParams
        import torch
        
        # 检查GPU
        if not torch.cuda.is_available():
            logger.error("CUDA不可用")
            return False
        
        logger.info("尝试加载小模型进行测试...")
        
        # 使用较小的模型进行测试
        test_model = "microsoft/DialoGPT-small"  # 约117MB
        
        try:
            llm = LLM(
                model=test_model,
                tensor_parallel_size=1,
                gpu_memory_utilization=0.3,
                max_model_len=512,
                trust_remote_code=True
            )
            
            sampling_params = SamplingParams(temperature=0.8, top_p=0.95, max_tokens=20)
            outputs = llm.generate(["Hello"], sampling_params)
            
            logger.success("vLLM基本功能测试成功")
            logger.info(f"测试输出: {outputs[0].outputs[0].text}")
            return True
            
        except Exception as e:
            logger.error(f"vLLM测试失败: {e}")
            return False
            
    except ImportError as e:
        logger.error(f"导入vLLM失败: {e}")
        return False


def test_hf_mirror_connection():
    """测试HuggingFace镜像连接"""
    logger.info("测试HuggingFace镜像连接...")
    
    try:
        import requests
        
        # 测试镜像连接
        response = requests.get("https://hf-mirror.com", timeout=10)
        if response.status_code == 200:
            logger.success("HuggingFace镜像连接正常")
            return True
        else:
            logger.error(f"镜像连接失败: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"镜像连接测试失败: {e}")
        return False


def test_model_download():
    """测试模型下载"""
    logger.info("测试模型下载...")
    
    try:
        from transformers import AutoTokenizer
        
        # 测试下载tokenizer
        tokenizer = AutoTokenizer.from_pretrained(
            "Qwen/Qwen2.5-7B-Instruct",
            trust_remote_code=True,
            cache_dir=os.environ.get("HUGGINGFACE_HUB_CACHE")
        )
        
        logger.success("模型tokenizer下载成功")
        logger.info(f"词汇表大小: {tokenizer.vocab_size}")
        return True
        
    except Exception as e:
        logger.error(f"模型下载失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("="*60)
    logger.info("vLLM中国镜像环境设置")
    logger.info("="*60)
    
    # 1. 设置环境
    setup_china_environment()
    
    # 2. 检查vLLM安装
    if not check_vllm_installation():
        logger.info("开始安装vLLM...")
        if not install_vllm():
            logger.error("vLLM安装失败")
            return False
    
    # 3. 测试HF镜像连接
    if not test_hf_mirror_connection():
        logger.warning("HuggingFace镜像连接失败，可能影响模型下载")
    
    # 4. 测试模型下载
    if test_model_download():
        logger.success("模型下载测试成功")
    else:
        logger.warning("模型下载测试失败")
    
    # 5. 测试vLLM基本功能
    if test_vllm_basic():
        logger.success("vLLM基本功能测试成功")
    else:
        logger.warning("vLLM基本功能测试失败")
    
    logger.info("="*60)
    logger.info("设置完成!")
    logger.info("="*60)
    logger.info("现在可以运行:")
    logger.info("  python demo_vllm_china.py")
    
    return True


if __name__ == "__main__":
    main()
