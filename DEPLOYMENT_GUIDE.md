# Qwen2.5-7B 部署指南

本指南提供了多种部署Qwen2.5-7B模型的方法，用于多智能体意图检测框架。

## 方案1：Ollama部署（推荐）

### 优点
- 安装简单，一键部署
- 自动模型管理
- 内存优化
- 支持多种模型格式
- 本地运行，数据安全

### 安装步骤

#### 1. 安装Ollama
```bash
# Linux/macOS
curl -fsSL https://ollama.com/install.sh | sh

# 或者手动下载
# https://ollama.com/download
```

#### 2. 启动Ollama服务
```bash
ollama serve
```

#### 3. 拉取Qwen2.5-7B模型
```bash
ollama pull qwen2.5:7b
```

#### 4. 测试模型
```bash
ollama run qwen2.5:7b "你好，请介绍一下你自己"
```

#### 5. 运行框架演示
```bash
conda activate LR
python demo_ollama.py
```

### 使用代码示例
```python
from src.utils.ollama_client import initialize_ollama_client

# 初始化客户端
client = initialize_ollama_client("qwen2.5:7b")

# 生成文本
response = client.generate("你的问题")
print(response)
```

## 方案2：vLLM部署（高性能）

### 优点
- 高吞吐量
- 支持批处理
- GPU加速
- 适合生产环境

### 安装步骤

#### 1. 安装vLLM
```bash
conda activate LR
pip install vllm
```

#### 2. 设置环境变量
```bash
export HF_ENDPOINT=https://hf-mirror.com
export CUDA_VISIBLE_DEVICES=0,1
```

#### 3. 运行演示
```bash
python demo_china.py --learning-only
```

### 配置文件
```yaml
model:
  name: "Qwen/Qwen2.5-7B-Instruct"
  hf_mirror: "https://hf-mirror.com"
  gpu_devices: [0, 1]
  tensor_parallel_size: 2
```

## 方案3：Transformers直接部署

### 优点
- 简单直接
- 易于调试
- 兼容性好

### 代码示例
```python
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch

# 加载模型
tokenizer = AutoTokenizer.from_pretrained(
    "Qwen/Qwen2.5-7B-Instruct",
    trust_remote_code=True
)

model = AutoModelForCausalLM.from_pretrained(
    "Qwen/Qwen2.5-7B-Instruct",
    torch_dtype=torch.float16,
    device_map="auto",
    trust_remote_code=True
)

# 生成文本
def generate_text(prompt, max_length=2048):
    inputs = tokenizer(prompt, return_tensors="pt")
    
    with torch.no_grad():
        outputs = model.generate(
            inputs.input_ids,
            max_length=max_length,
            temperature=0.2,
            top_p=0.9,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id
        )
    
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    return response[len(prompt):].strip()
```

## 方案4：Docker部署

### Dockerfile示例
```dockerfile
FROM nvidia/cuda:11.8-devel-ubuntu22.04

# 安装Python和依赖
RUN apt-get update && apt-get install -y \
    python3 python3-pip curl \
    && rm -rf /var/lib/apt/lists/*

# 安装Ollama
RUN curl -fsSL https://ollama.com/install.sh | sh

# 复制项目文件
COPY . /app
WORKDIR /app

# 安装Python依赖
RUN pip3 install -r requirements.txt

# 启动脚本
COPY start.sh /start.sh
RUN chmod +x /start.sh

EXPOSE 11434
CMD ["/start.sh"]
```

### start.sh
```bash
#!/bin/bash
# 启动Ollama服务
ollama serve &

# 等待服务启动
sleep 10

# 拉取模型
ollama pull qwen2.5:7b

# 保持容器运行
tail -f /dev/null
```

## 方案5：API服务部署

### 使用FastAPI创建API服务
```python
from fastapi import FastAPI
from pydantic import BaseModel
from src.utils.ollama_client import initialize_ollama_client

app = FastAPI()
client = initialize_ollama_client("qwen2.5:7b")

class GenerateRequest(BaseModel):
    prompt: str
    temperature: float = 0.2
    max_tokens: int = 2048

@app.post("/generate")
async def generate(request: GenerateRequest):
    response = client.generate(
        request.prompt,
        config=OllamaGenerationConfig(
            temperature=request.temperature,
            max_tokens=request.max_tokens
        )
    )
    return {"response": response}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

## 性能对比

| 方案 | 部署难度 | 性能 | 内存使用 | 适用场景 |
|------|----------|------|----------|----------|
| Ollama | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | 开发测试 |
| vLLM | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | 生产环境 |
| Transformers | ⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐ | 简单应用 |
| Docker | ⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | 容器化部署 |
| API服务 | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | 微服务架构 |

## 故障排除

### 常见问题

#### 1. Ollama模型下载失败
```bash
# 设置代理
export https_proxy=http://your-proxy:port
ollama pull qwen2.5:7b
```

#### 2. GPU内存不足
```bash
# 减少模型精度
ollama pull qwen2.5:7b-q4_0  # 4位量化版本
```

#### 3. vLLM多进程错误
```yaml
# 使用单GPU
model:
  gpu_devices: [0]
  tensor_parallel_size: 1
```

#### 4. 网络连接问题
```bash
# 使用中国镜像
export HF_ENDPOINT=https://hf-mirror.com
```

## 推荐配置

### 开发环境
- **方案**: Ollama
- **模型**: qwen2.5:7b
- **配置**: 默认设置

### 生产环境
- **方案**: vLLM + Docker
- **模型**: Qwen/Qwen2.5-7B-Instruct
- **配置**: 多GPU并行

### 资源受限环境
- **方案**: Ollama + 量化模型
- **模型**: qwen2.5:7b-q4_0
- **配置**: 单GPU

## 下一步

1. 选择适合的部署方案
2. 按照对应的安装步骤操作
3. 运行测试脚本验证部署
4. 根据需要调整配置参数
5. 集成到多智能体框架中

更多详细信息请参考各方案的具体文档和示例代码。
