#!/usr/bin/env python3
"""
优化的vLLM演示脚本，解决内存问题
"""
import os
import sys
import time
import torch
from pathlib import Path
from loguru import logger

# 设置中国镜像
os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"
os.environ["HUGGINGFACE_HUB_CACHE"] = os.path.expanduser("~/.cache/huggingface")

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))


def check_gpu_memory():
    """检查GPU内存状态"""
    if not torch.cuda.is_available():
        logger.error("CUDA不可用")
        return False
    
    logger.info("GPU内存状态:")
    for i in range(torch.cuda.device_count()):
        props = torch.cuda.get_device_properties(i)
        total_memory = props.total_memory / 1024**3
        
        # 清理并检查可用内存
        torch.cuda.set_device(i)
        torch.cuda.empty_cache()
        
        allocated = torch.cuda.memory_allocated(i) / 1024**3
        reserved = torch.cuda.memory_reserved(i) / 1024**3
        available = total_memory - reserved
        
        logger.info(f"GPU {i}: {props.name}")
        logger.info(f"  总显存: {total_memory:.1f} GB")
        logger.info(f"  已用: {allocated:.1f} GB")
        logger.info(f"  可用: {available:.1f} GB")
        
        if available < 10:
            logger.warning(f"GPU {i} 可用内存不足，建议清理")
    
    return True


def optimize_vllm_config():
    """优化vLLM配置"""
    logger.info("优化vLLM配置...")
    
    # 清理所有GPU内存
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            torch.cuda.set_device(i)
            torch.cuda.empty_cache()
    
    # 设置环境变量
    os.environ["CUDA_VISIBLE_DEVICES"] = "0"  # 只使用GPU0
    os.environ["VLLM_USE_MODELSCOPE"] = "False"
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    
    # 优化的配置
    config = {
        "tensor_parallel_size": 1,
        "gpu_memory_utilization": 0.75,
        "dtype": "half",
        "max_model_len": 2048,
        "enforce_eager": True,
        "disable_custom_all_reduce": True
    }
    
    logger.info("vLLM优化配置:")
    for key, value in config.items():
        logger.info(f"  {key}: {value}")
    
    return config


def run_vllm_demo():
    """运行优化的vLLM演示"""
    
    logger.info("="*60)
    logger.info("多智能体意图检测框架 - 优化vLLM演示")
    logger.info("="*60)
    
    try:
        # 1. 检查GPU内存
        if not check_gpu_memory():
            return False
        
        # 2. 优化配置
        vllm_config = optimize_vllm_config()
        
        # 3. 初始化框架
        logger.info("初始化框架...")
        from src.framework import MultiAgentIntentDetectionFramework
        
        # 修改配置文件
        config_path = "config_vllm_optimized.yaml"
        create_optimized_config(config_path, vllm_config)
        
        framework = MultiAgentIntentDetectionFramework(config_path)
        
        # 4. 运行学习阶段
        logger.info("开始学习阶段...")
        start_time = time.time()
        
        framework.initialize()
        principles = framework.run_learning_phase()
        
        learning_time = time.time() - start_time
        logger.info(f"学习阶段完成: {learning_time:.2f}秒")
        logger.info(f"学习到 {len(principles)} 个原则")
        
        # 显示原则
        for i, principle in enumerate(principles):
            logger.info(f"{i+1}. {principle.content}")
        
        # 5. 运行测试阶段（小规模）
        logger.info("开始测试阶段...")
        start_time = time.time()
        
        results = framework.run_testing_phase(principles)
        
        testing_time = time.time() - start_time
        logger.info(f"测试阶段完成: {testing_time:.2f}秒")
        
        # 显示结果
        if 'overall_metrics' in results:
            metrics = results['overall_metrics']
            logger.info("="*60)
            logger.info("最终结果")
            logger.info("="*60)
            logger.info(f"准确率: {metrics['accuracy']:.4f}")
            logger.info(f"F1分数: {metrics['f1_macro']:.4f}")
            logger.info(f"精确率: {metrics['precision_macro']:.4f}")
            logger.info(f"召回率: {metrics['recall_macro']:.4f}")
        
        logger.info("🎉 vLLM演示成功完成!")
        return True
        
    except Exception as e:
        logger.error(f"vLLM演示失败: {e}")
        logger.exception("详细错误:")
        
        # 内存诊断
        if "out of memory" in str(e).lower() or "oom" in str(e).lower():
            logger.error("检测到内存不足错误!")
            logger.info("建议解决方案:")
            logger.info("1. 减少gpu_memory_utilization (当前0.75)")
            logger.info("2. 减少max_model_len (当前2048)")
            logger.info("3. 使用量化模型")
            logger.info("4. 检查其他进程占用GPU内存")
        
        return False


def create_optimized_config(config_path: str, vllm_config: dict):
    """创建优化的配置文件"""
    
    config_content = f"""# 优化的vLLM配置

# Model Configuration
model:
  name: "Qwen/Qwen2.5-7B-Instruct"
  hf_mirror: "https://hf-mirror.com"
  temperature: 0.2
  top_p: 0.9
  max_tokens: 1024
  gpu_devices: [0]
  tensor_parallel_size: {vllm_config['tensor_parallel_size']}
  dtype: "{vllm_config['dtype']}"

# Data Configuration
data:
  train_path: "stackoverflow/train.tsv"
  test_path: "stackoverflow/test.tsv"
  dev_path: "stackoverflow/dev.tsv"
  intent_labels: [
    "wordpress", "oracle", "svn", "apache", "excel", "matlab",
    "visual-studio", "cocoa", "osx", "bash", "spring", "hibernate",
    "scala", "sharepoint", "ajax", "qt", "drupal", "linq", "haskell", "magento"
  ]
  examples_per_intent: 1
  max_text_length: 256
  demo_test_samples: 50

# Agent Configuration
agents:
  student:
    count: 2  # 减少智能体数量
    strategies: ["analytical", "pattern_based"]
    max_principles: 3
  
  teacher:
    evaluation_dimensions: 2
    dimension_weights: [0.5, 0.5]
    quality_threshold: 0.6
    consolidation_strategy: "weighted_synthesis"
  
  exam:
    batch_size: 8  # 减少批大小
    evaluation_metrics: ["accuracy", "f1_macro", "f1_micro", "precision", "recall"]

# Framework Configuration
framework:
  max_iterations: 1
  convergence_threshold: 0.95
  parallel_processing: false  # 避免多进程冲突
  save_intermediate_results: true
  output_dir: "vllm_results"

# Logging Configuration
logging:
  level: "INFO"
  format: "{{time:YYYY-MM-DD HH:mm:ss}} | {{level}} | {{name}} | {{message}}"
  file: "logs/vllm_framework.log"
  rotation: "1 day"
  retention: "7 days"
"""
    
    with open(config_path, 'w') as f:
        f.write(config_content)
    
    logger.info(f"创建优化配置文件: {config_path}")


def main():
    """主函数"""
    
    # 设置日志
    logger.remove()
    logger.add(sys.stdout, level="INFO", 
               format="{time:HH:mm:ss} | {level} | {message}")
    
    logger.info("vLLM内存优化演示")
    
    # 运行内存诊断
    logger.info("运行内存诊断...")
    os.system("python debug_vllm_memory.py")
    
    print("\n" + "="*60)
    input("按Enter键继续运行vLLM演示...")
    
    success = run_vllm_demo()
    
    if success:
        logger.info("\n🎉 演示成功完成!")
        logger.info("\n优化要点:")
        logger.info("- 使用单GPU避免多进程冲突")
        logger.info("- 降低gpu_memory_utilization到0.75")
        logger.info("- 减少max_model_len节省KV缓存")
        logger.info("- 启用enforce_eager避免CUDA图问题")
    else:
        logger.error("\n❌ 演示失败!")
        logger.info("\n故障排除:")
        logger.info("1. 运行 python debug_vllm_memory.py 检查内存")
        logger.info("2. 检查是否有其他进程占用GPU")
        logger.info("3. 尝试重启Python进程清理内存")
        logger.info("4. 考虑使用Transformers替代方案")
        sys.exit(1)


if __name__ == "__main__":
    main()
