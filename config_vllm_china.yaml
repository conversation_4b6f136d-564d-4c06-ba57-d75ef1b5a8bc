# 中国大陆镜像vLLM配置

# Model Configuration
model:
  name: "Qwen/Qwen2.5-7B-Instruct"
  hf_mirror: "https://hf-mirror.com"  # 中国大陆镜像
  hf_endpoint: "https://hf-mirror.com"
  temperature: 0.2
  top_p: 0.9
  max_tokens: 1024
  gpu_devices: [0]  # 单GPU避免多进程问题
  tensor_parallel_size: 1
  dtype: "half"
  gpu_memory_utilization: 0.8  # 80%显存使用率
  max_model_len: 2048  # 减少KV缓存内存

# Data Configuration
data:
  train_path: "stackoverflow/train.tsv"
  test_path: "stackoverflow/test.tsv"
  dev_path: "stackoverflow/dev.tsv"
  intent_labels: [
    "wordpress", "oracle", "svn", "apache", "excel", "matlab",
    "visual-studio", "cocoa", "osx", "bash", "spring", "hibernate",
    "scala", "sharepoint", "ajax", "qt", "drupal", "linq", "haskell", "magento"
  ]
  examples_per_intent: 1
  max_text_length: 256
  demo_test_samples: 30  # 30个测试样本

# Agent Configuration
agents:
  student:
    count: 2  # 减少智能体数量节省时间
    strategies: ["analytical", "pattern_based"]
    max_principles: 3  # 每个智能体3个原则
  
  teacher:
    evaluation_dimensions: 2
    dimension_weights: [0.5, 0.5]
    quality_threshold: 0.6
    consolidation_strategy: "weighted_synthesis"
  
  exam:
    batch_size: 5  # 小批量处理
    evaluation_metrics: ["accuracy", "f1_macro", "f1_micro", "precision", "recall"]

# Framework Configuration
framework:
  max_iterations: 1
  convergence_threshold: 0.95
  parallel_processing: false  # 避免多进程冲突
  save_intermediate_results: true
  output_dir: "vllm_china_results"

# Logging Configuration
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name} | {message}"
  file: "logs/vllm_china_framework.log"
  rotation: "1 day"
  retention: "7 days"
