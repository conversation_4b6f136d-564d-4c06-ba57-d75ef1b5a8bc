{"experiment_name": "mock_demo", "overall_metrics": {"accuracy": 0.76, "f1_macro": 0.7640015540015541, "f1_micro": 0.76, "f1_weighted": 0.764001554001554, "precision_macro": 0.7808333333333335, "precision_micro": 0.76, "recall_macro": 0.76, "recall_micro": 0.76}, "per_class_metrics": {"wordpress": {"precision": 0.8, "recall": 0.8, "f1": 0.8, "support": 5.0}, "oracle": {"precision": 0.6666666666666666, "recall": 0.8, "f1": 0.7272727272727273, "support": 5.0}, "svn": {"precision": 0.75, "recall": 0.6, "f1": 0.6666666666666666, "support": 5.0}, "apache": {"precision": 1.0, "recall": 0.8, "f1": 0.8888888888888888, "support": 5.0}, "excel": {"precision": 0.75, "recall": 0.6, "f1": 0.6666666666666666, "support": 5.0}, "matlab": {"precision": 0.6, "recall": 0.6, "f1": 0.6, "support": 5.0}, "visual-studio": {"precision": 0.8, "recall": 0.8, "f1": 0.8, "support": 5.0}, "cocoa": {"precision": 0.8, "recall": 0.8, "f1": 0.8, "support": 5.0}, "osx": {"precision": 0.8, "recall": 0.8, "f1": 0.8, "support": 5.0}, "bash": {"precision": 0.6666666666666666, "recall": 0.8, "f1": 0.7272727272727273, "support": 5.0}, "spring": {"precision": 0.8, "recall": 0.8, "f1": 0.8, "support": 5.0}, "hibernate": {"precision": 0.75, "recall": 0.6, "f1": 0.6666666666666666, "support": 5.0}, "scala": {"precision": 1.0, "recall": 0.8, "f1": 0.8888888888888888, "support": 5.0}, "sharepoint": {"precision": 1.0, "recall": 0.8, "f1": 0.8888888888888888, "support": 5.0}, "ajax": {"precision": 0.5, "recall": 0.8, "f1": 0.6153846153846154, "support": 5.0}, "qt": {"precision": 0.8, "recall": 0.8, "f1": 0.8, "support": 5.0}, "drupal": {"precision": 0.6666666666666666, "recall": 0.8, "f1": 0.7272727272727273, "support": 5.0}, "linq": {"precision": 0.6666666666666666, "recall": 0.8, "f1": 0.7272727272727273, "support": 5.0}, "haskell": {"precision": 0.8, "recall": 0.8, "f1": 0.8, "support": 5.0}, "magento": {"precision": 1.0, "recall": 0.8, "f1": 0.8888888888888888, "support": 5.0}}, "confusion_matrix": [[4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0], [0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], [0, 0, 3, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0], [0, 0, 0, 4, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], [0, 1, 0, 0, 0, 3, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 4, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0], [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 1, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 1, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0], [0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 4, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 4]], "error_analysis": {"total_errors": 24, "total_correct": 76, "error_rate": 0.24, "errors": [{"index": 3, "true_label": "hibernate", "pred_label": "linq", "correct": false}, {"index": 4, "true_label": "bash", "pred_label": "drupal", "correct": false}, {"index": 6, "true_label": "excel", "pred_label": "oracle", "correct": false}, {"index": 9, "true_label": "spring", "pred_label": "excel", "correct": false}, {"index": 11, "true_label": "sharepoint", "pred_label": "bash", "correct": false}, {"index": 17, "true_label": "visual-studio", "pred_label": "cocoa", "correct": false}, {"index": 23, "true_label": "svn", "pred_label": "linq", "correct": false}, {"index": 25, "true_label": "scala", "pred_label": "drupal", "correct": false}, {"index": 31, "true_label": "cocoa", "pred_label": "spring", "correct": false}, {"index": 44, "true_label": "excel", "pred_label": "ajax", "correct": false}, {"index": 46, "true_label": "svn", "pred_label": "matlab", "correct": false}, {"index": 50, "true_label": "drupal", "pred_label": "ajax", "correct": false}, {"index": 53, "true_label": "matlab", "pred_label": "osx", "correct": false}, {"index": 64, "true_label": "osx", "pred_label": "haskell", "correct": false}, {"index": 69, "true_label": "oracle", "pred_label": "ajax", "correct": false}, {"index": 71, "true_label": "apache", "pred_label": "bash", "correct": false}, {"index": 72, "true_label": "wordpress", "pred_label": "qt", "correct": false}, {"index": 76, "true_label": "haskell", "pred_label": "hibernate", "correct": false}, {"index": 79, "true_label": "matlab", "pred_label": "oracle", "correct": false}, {"index": 80, "true_label": "hibernate", "pred_label": "wordpress", "correct": false}, {"index": 82, "true_label": "magento", "pred_label": "ajax", "correct": false}, {"index": 83, "true_label": "linq", "pred_label": "visual-studio", "correct": false}, {"index": 84, "true_label": "qt", "pred_label": "matlab", "correct": false}, {"index": 93, "true_label": "ajax", "pred_label": "svn", "correct": false}], "error_patterns": {"hibernate -> linq": 1, "bash -> drupal": 1, "excel -> oracle": 1, "spring -> excel": 1, "sharepoint -> bash": 1, "visual-studio -> cocoa": 1, "svn -> linq": 1, "scala -> drupal": 1, "cocoa -> spring": 1, "excel -> ajax": 1, "svn -> matlab": 1, "drupal -> ajax": 1, "matlab -> osx": 1, "osx -> haskell": 1, "oracle -> ajax": 1, "apache -> bash": 1, "wordpress -> qt": 1, "haskell -> hibernate": 1, "matlab -> oracle": 1, "hibernate -> wordpress": 1, "magento -> ajax": 1, "linq -> visual-studio": 1, "qt -> matlab": 1, "ajax -> svn": 1}, "most_confused_pairs": [["hibernate -> linq", 1], ["bash -> drupal", 1], ["excel -> oracle", 1], ["spring -> excel", 1], ["sharepoint -> bash", 1], ["visual-studio -> cocoa", 1], ["svn -> linq", 1], ["scala -> drupal", 1], ["cocoa -> spring", 1], ["excel -> ajax", 1]]}, "dataset_stats": {"total_samples": 100, "num_classes": 20, "true_distribution": {"excel": 5, "bash": 5, "hibernate": 5, "ajax": 5, "apache": 5, "oracle": 5, "spring": 5, "sharepoint": 5, "matlab": 5, "svn": 5, "visual-studio": 5, "cocoa": 5, "scala": 5, "haskell": 5, "osx": 5, "drupal": 5, "wordpress": 5, "qt": 5, "linq": 5, "magento": 5}, "pred_distribution": {"excel": 4, "bash": 6, "hibernate": 4, "linq": 6, "drupal": 6, "ajax": 8, "oracle": 6, "apache": 4, "sharepoint": 4, "matlab": 5, "spring": 5, "svn": 4, "visual-studio": 5, "cocoa": 5, "haskell": 5, "osx": 5, "wordpress": 5, "qt": 5, "magento": 4, "scala": 4}}}