#!/usr/bin/env python3
"""
Setup script for China mainland HuggingFace mirror
"""
import os
import sys
from pathlib import Path

def setup_china_mirror():
    """Setup environment variables for China mainland HuggingFace mirror"""
    
    # HuggingFace mirror settings
    mirror_url = "https://hf-mirror.com"
    
    # Set environment variables
    env_vars = {
        "HF_ENDPOINT": mirror_url,
        "HUGGINGFACE_HUB_CACHE": os.path.expanduser("~/.cache/huggingface"),
        "HF_HUB_OFFLINE": "0",
        "TRANSFORMERS_OFFLINE": "0",
        "HF_HUB_DISABLE_TELEMETRY": "1",
        "TOKENIZERS_PARALLELISM": "false"  # Avoid warnings
    }
    
    print("Setting up China mainland HuggingFace mirror...")
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"  {key} = {value}")
    
    # Create cache directory if it doesn't exist
    cache_dir = Path(env_vars["HUGGINGFACE_HUB_CACHE"])
    cache_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"Cache directory: {cache_dir}")
    print("Mirror setup completed!")
    
    return env_vars

def test_mirror_connection():
    """Test connection to the mirror"""
    try:
        import requests
        mirror_url = "https://hf-mirror.com"
        
        print(f"Testing connection to {mirror_url}...")
        response = requests.get(f"{mirror_url}/api/models/Qwen/Qwen2.5-7B-Instruct", timeout=10)
        
        if response.status_code == 200:
            print("✓ Mirror connection successful!")
            return True
        else:
            print(f"✗ Mirror returned status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ Mirror connection failed: {e}")
        return False

def download_model_info():
    """Download model information to test the setup"""
    try:
        from transformers import AutoTokenizer
        
        print("Testing model download with mirror...")
        
        # Try to load tokenizer (this will test the mirror)
        tokenizer = AutoTokenizer.from_pretrained(
            "Qwen/Qwen2.5-7B-Instruct",
            trust_remote_code=True,
            cache_dir=os.environ.get("HUGGINGFACE_HUB_CACHE")
        )
        
        print("✓ Model tokenizer loaded successfully!")
        print(f"Vocabulary size: {tokenizer.vocab_size}")
        return True
        
    except Exception as e:
        print(f"✗ Model download failed: {e}")
        return False

def main():
    """Main function"""
    print("="*60)
    print("China Mainland HuggingFace Mirror Setup")
    print("="*60)
    
    # Setup mirror
    env_vars = setup_china_mirror()
    
    # Test connection
    if test_mirror_connection():
        print("\n" + "="*60)
        print("Testing model download...")
        print("="*60)
        
        if download_model_info():
            print("\n🎉 Setup completed successfully!")
            print("\nYou can now run the demo:")
            print("  conda activate LR && python demo_run.py --learning-only")
        else:
            print("\n⚠️  Setup completed but model download test failed.")
            print("The demo might still work if the model is cached.")
    else:
        print("\n❌ Mirror connection failed.")
        print("Please check your internet connection and try again.")
    
    print("\nEnvironment variables set:")
    for key, value in env_vars.items():
        print(f"  export {key}='{value}'")

if __name__ == "__main__":
    main()
