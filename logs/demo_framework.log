2025-07-13 06:11:58 | INFO | src.utils.logging_setup | Logging setup completed
2025-07-13 06:11:58 | INFO | src.utils.logging_setup | Log level: INFO
2025-07-13 06:11:58 | INFO | src.utils.logging_setup | Log file: logs/demo_framework.log
2025-07-13 06:11:58 | INFO | src.framework | Multi-Agent Intent Detection Framework initialized
2025-07-13 06:11:58 | INFO | __main__ | Starting learning phase...
2025-07-13 06:11:58 | INFO | src.framework | Initializing framework components...
2025-07-13 06:11:58 | INFO | src.framework | Initializing LLM client...
2025-07-13 06:11:58 | INFO | src.utils.llm_client | Using GPUs: [0, 1]
2025-07-13 06:11:58 | INFO | src.utils.llm_client | Tensor parallel size: 2
2025-07-13 06:11:58 | INFO | src.utils.llm_client | Loading model: Qwen/Qwen2.5-7B-Instruct
2025-07-13 06:14:47 | ERROR | src.utils.llm_client | Failed to load model: (MaxRetryError("HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/Qwen/Qwen2.5-7B-Instruct/tree/main/additional_chat_templates?recursive=False&expand=False (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>, 'Connection to huggingface.co timed out. (connect timeout=None)'))"), '(Request ID: b6926599-0aab-41ed-81eb-e95b646466bb)')
2025-07-13 06:14:47 | ERROR | src.utils.logging_setup | initialize failed after 169.83 seconds: (MaxRetryError("HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/Qwen/Qwen2.5-7B-Instruct/tree/main/additional_chat_templates?recursive=False&expand=False (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>, 'Connection to huggingface.co timed out. (connect timeout=None)'))"), '(Request ID: b6926599-0aab-41ed-81eb-e95b646466bb)')
2025-07-13 06:14:47 | ERROR | __main__ | Learning demo failed: (MaxRetryError("HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/Qwen/Qwen2.5-7B-Instruct/tree/main/additional_chat_templates?recursive=False&expand=False (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>, 'Connection to huggingface.co timed out. (connect timeout=None)'))"), '(Request ID: b6926599-0aab-41ed-81eb-e95b646466bb)')
2025-07-13 06:14:47 | ERROR | __main__ | Full traceback:
Traceback (most recent call last):

  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           │          └ <function create_connection at 0x7f093fe2df80>
           └ <module 'urllib3.util.connection' from '/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/util/connection.py'>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/util/connection.py", line 85, in create_connection
    raise err
          └ None
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/util/connection.py", line 73, in create_connection
    sock.connect(sa)
    │    │       └ ('128.242.245.125', 443)
    │    └ <method 'connect' of '_socket.socket' objects>
    └ <socket.socket [closed] fd=-1, family=2, type=1, proto=6>

TimeoutError: [Errno 110] Connection timed out


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    response = self._make_request(
               │    └ <function HTTPConnectionPool._make_request at 0x7f093fd11800>
               └ <urllib3.connectionpool.HTTPSConnectionPool object at 0x7f08bd3e57f0>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connectionpool.py", line 488, in _make_request
    raise new_e
          └ ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>, 'Connection to huggingface.co timed out. (...
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
    │    │              └ <urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>
    │    └ <function HTTPSConnectionPool._validate_conn at 0x7f093fd11da0>
    └ <urllib3.connectionpool.HTTPSConnectionPool object at 0x7f08bd3e57f0>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connectionpool.py", line 1093, in _validate_conn
    conn.connect()
    │    └ <function HTTPSConnection.connect at 0x7f093fd098a0>
    └ <urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connection.py", line 753, in connect
    self.sock = sock = self._new_conn()
    │    │             │    └ <function HTTPConnection._new_conn at 0x7f093fd08ea0>
    │    │             └ <urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>
    │    └ None
    └ <urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connection.py", line 207, in _new_conn
    raise ConnectTimeoutError(
          └ <class 'urllib3.exceptions.ConnectTimeoutError'>

urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>, 'Connection to huggingface.co timed out. (connect timeout=None)')


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
           │    └ <function HTTPConnectionPool.urlopen at 0x7f093fd119e0>
           └ <urllib3.connectionpool.HTTPSConnectionPool object at 0x7f08bd3e57f0>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              │       └ <function Retry.increment at 0x7f093fe4c860>
              └ Retry(total=0, connect=None, read=False, redirect=None, status=None)
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/util/retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
          │             │      │    │            └ ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>, 'Connection to huggingface.co timed out. (...
          │             │      │    └ ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>, 'Connection to huggingface.co timed out. (...
          │             │      └ '/api/models/Qwen/Qwen2.5-7B-Instruct/tree/main/additional_chat_templates?recursive=False&expand=False'
          │             └ <urllib3.connectionpool.HTTPSConnectionPool object at 0x7f08bd3e57f0>
          └ <class 'urllib3.exceptions.MaxRetryError'>

urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/Qwen/Qwen2.5-7B-Instruct/tree/main/additional_chat_templates?recursive=False&expand=False (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>, 'Connection to huggingface.co timed out. (connect timeout=None)'))


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "/home/<USER>/exp/L1/demo_run.py", line 213, in <module>
    main()
    └ <function main at 0x7f09221db9c0>

  File "/home/<USER>/exp/L1/demo_run.py", line 201, in main
    success = run_learning_only_demo()
              └ <function run_learning_only_demo at 0x7f094bdb9440>

> File "/home/<USER>/exp/L1/demo_run.py", line 154, in run_learning_only_demo
    framework.initialize()
    │         └ <function log_execution_time.<locals>.wrapper at 0x7f09221db1a0>
    └ <src.framework.MultiAgentIntentDetectionFramework object at 0x7f092210f560>

  File "/home/<USER>/exp/L1/src/utils/logging_setup.py", line 119, in wrapper
    result = func(*args, **kwargs)
             │     │       └ {}
             │     └ (<src.framework.MultiAgentIntentDetectionFramework object at 0x7f092210f560>,)
             └ <function MultiAgentIntentDetectionFramework.initialize at 0x7f08be17c680>

  File "/home/<USER>/exp/L1/src/framework.py", line 55, in initialize
    initialize_llm_client(
    └ <function initialize_llm_client at 0x7f08be1053a0>

  File "/home/<USER>/exp/L1/src/utils/llm_client.py", line 194, in initialize_llm_client
    _llm_client.initialize_model()
    │           └ <function VLLMClient.initialize_model at 0x7f08be105120>
    └ <src.utils.llm_client.VLLMClient object at 0x7f0922135130>

  File "/home/<USER>/exp/L1/src/utils/llm_client.py", line 61, in initialize_model
    self.llm = LLM(
    │    │     └ <class 'vllm.entrypoints.llm.LLM'>
    │    └ None
    └ <src.utils.llm_client.VLLMClient object at 0x7f0922135130>

  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/entrypoints/llm.py", line 178, in __init__
    self.llm_engine = LLMEngine.from_engine_args(
    │                 │         └ <classmethod(<function LLMEngine.from_engine_args at 0x7f08bed079c0>)>
    │                 └ <class 'vllm.engine.llm_engine.LLMEngine'>
    └ <vllm.entrypoints.llm.LLM object at 0x7f09221b6870>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/engine/llm_engine.py", line 550, in from_engine_args
    engine = cls(
             └ <class 'vllm.engine.llm_engine.LLMEngine'>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/engine/llm_engine.py", line 291, in __init__
    self.tokenizer = self._init_tokenizer()
    │                │    └ <function LLMEngine._init_tokenizer at 0x7f08bed07ce0>
    │                └ <vllm.engine.llm_engine.LLMEngine object at 0x7f08bd2b58e0>
    └ <vllm.engine.llm_engine.LLMEngine object at 0x7f08bd2b58e0>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/engine/llm_engine.py", line 594, in _init_tokenizer
    return init_tokenizer_from_configs(
           └ <function init_tokenizer_from_configs at 0x7f08c14f67a0>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/transformers_utils/tokenizer_group/__init__.py", line 28, in init_tokenizer_from_configs
    return get_tokenizer_group(parallel_config.tokenizer_pool_config,
           │                   │               └ None
           │                   └ <vllm.config.ParallelConfig object at 0x7f09221814c0>
           └ <function get_tokenizer_group at 0x7f08bef79440>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/transformers_utils/tokenizer_group/__init__.py", line 49, in get_tokenizer_group
    return tokenizer_cls.from_config(tokenizer_pool_config, **init_kwargs)
           │             │           │                        └ {'tokenizer_id': 'Qwen/Qwen2.5-7B-Instruct', 'enable_lora': False, 'max_num_seqs': 256, 'max_input_length': None, 'tokenizer_...
           │             │           └ None
           │             └ <classmethod(<function TokenizerGroup.from_config at 0x7f08bef79580>)>
           └ <class 'vllm.transformers_utils.tokenizer_group.tokenizer_group.TokenizerGroup'>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/transformers_utils/tokenizer_group/tokenizer_group.py", line 30, in from_config
    return cls(**init_kwargs)
           │     └ {'tokenizer_id': 'Qwen/Qwen2.5-7B-Instruct', 'enable_lora': False, 'max_num_seqs': 256, 'max_input_length': None, 'tokenizer_...
           └ <class 'vllm.transformers_utils.tokenizer_group.tokenizer_group.TokenizerGroup'>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/transformers_utils/tokenizer_group/tokenizer_group.py", line 23, in __init__
    self.tokenizer = get_tokenizer(self.tokenizer_id, **tokenizer_config)
    │                │             │    │               └ {'tokenizer_mode': 'auto', 'trust_remote_code': True, 'revision': None}
    │                │             │    └ 'Qwen/Qwen2.5-7B-Instruct'
    │                │             └ <vllm.transformers_utils.tokenizer_group.tokenizer_group.TokenizerGroup object at 0x7f0922135310>
    │                └ <function get_tokenizer at 0x7f08ca962b60>
    └ <vllm.transformers_utils.tokenizer_group.tokenizer_group.TokenizerGroup object at 0x7f0922135310>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/transformers_utils/tokenizer.py", line 120, in get_tokenizer
    tokenizer = AutoTokenizer.from_pretrained(
                │             └ <classmethod(<function AutoTokenizer.from_pretrained at 0x7f08ca9a8720>)>
                └ <class 'transformers.models.auto.tokenization_auto.AutoTokenizer'>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/transformers/models/auto/tokenization_auto.py", line 1050, in from_pretrained
    return tokenizer_class.from_pretrained(pretrained_model_name_or_path, *inputs, **kwargs)
           │               │               │                               │         └ {'revision': None, 'truncation_side': 'left', '_from_auto': True, '_commit_hash': 'a09a35458c702b33eeacc393d103063234e8bc28'}
           │               │               │                               └ ()
           │               │               └ 'Qwen/Qwen2.5-7B-Instruct'
           │               └ <classmethod(<function PreTrainedTokenizerBase.from_pretrained at 0x7f093eebd440>)>
           └ <class 'transformers.models.qwen2.tokenization_qwen2_fast.Qwen2TokenizerFast'>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/transformers/tokenization_utils_base.py", line 1957, in from_pretrained
    for template in list_repo_templates(
                    └ <function list_repo_templates at 0x7f093f5cfc40>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/transformers/utils/hub.py", line 163, in list_repo_templates
    for entry in list_repo_tree(
                 └ <bound method HfApi.list_repo_tree of <huggingface_hub.hf_api.HfApi object at 0x7f093f9ac800>>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/huggingface_hub/hf_api.py", line 3168, in list_repo_tree
    for path_info in paginate(path=tree_url, headers=headers, params={"recursive": recursive, "expand": expand}):
                     │             │                 │                             │                    └ False
                     │             │                 │                             └ False
                     │             │                 └ {'user-agent': 'unknown/None; hf_hub/0.33.4; python/3.12.11; torch/2.4.0+cu118'}
                     │             └ 'https://huggingface.co/api/models/Qwen/Qwen2.5-7B-Instruct/tree/main/additional_chat_templates'
                     └ <function paginate at 0x7f093fc03c40>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/huggingface_hub/utils/_pagination.py", line 36, in paginate
    r = session.get(path, params=params, headers=headers)
        │       │   │            │               └ {'user-agent': 'unknown/None; hf_hub/0.33.4; python/3.12.11; torch/2.4.0+cu118'}
        │       │   │            └ {'recursive': False, 'expand': False}
        │       │   └ 'https://huggingface.co/api/models/Qwen/Qwen2.5-7B-Instruct/tree/main/additional_chat_templates'
        │       └ <function Session.get at 0x7f093fbf7740>
        └ <requests.sessions.Session object at 0x7f092210c170>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/requests/sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           │    │              │      └ {'params': {'recursive': False, 'expand': False}, 'headers': {'user-agent': 'unknown/None; hf_hub/0.33.4; python/3.12.11; tor...
           │    │              └ 'https://huggingface.co/api/models/Qwen/Qwen2.5-7B-Instruct/tree/main/additional_chat_templates'
           │    └ <function Session.request at 0x7f093fbf76a0>
           └ <requests.sessions.Session object at 0x7f092210c170>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           │    │    │       └ {'timeout': None, 'allow_redirects': True, 'proxies': OrderedDict(), 'stream': False, 'verify': True, 'cert': None}
           │    │    └ <PreparedRequest [GET]>
           │    └ <function Session.send at 0x7f093fbf7ba0>
           └ <requests.sessions.Session object at 0x7f092210c170>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        │       │    │          └ {'timeout': None, 'proxies': OrderedDict(), 'stream': False, 'verify': True, 'cert': None}
        │       │    └ <PreparedRequest [GET]>
        │       └ <function UniqueRequestIdAdapter.send at 0x7f093fad8180>
        └ <huggingface_hub.utils._http.UniqueRequestIdAdapter object at 0x7f0921fb88f0>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/huggingface_hub/utils/_http.py", line 96, in send
    return super().send(request, *args, **kwargs)
                        │         │       └ {'timeout': None, 'proxies': OrderedDict(), 'stream': False, 'verify': True, 'cert': None}
                        │         └ ()
                        └ <PreparedRequest [GET]>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/requests/adapters.py", line 688, in send
    raise ConnectTimeout(e, request=request)
          │                         └ <PreparedRequest [GET]>
          └ <class 'requests.exceptions.ConnectTimeout'>

requests.exceptions.ConnectTimeout: (MaxRetryError("HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/Qwen/Qwen2.5-7B-Instruct/tree/main/additional_chat_templates?recursive=False&expand=False (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>, 'Connection to huggingface.co timed out. (connect timeout=None)'))"), '(Request ID: b6926599-0aab-41ed-81eb-e95b646466bb)')
2025-07-13 06:14:48 | ERROR | __main__ | ❌ Demo failed!
