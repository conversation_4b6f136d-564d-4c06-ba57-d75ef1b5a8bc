2025-07-13 06:11:58 | INFO | src.utils.logging_setup | Logging setup completed
2025-07-13 06:11:58 | INFO | src.utils.logging_setup | Log level: INFO
2025-07-13 06:11:58 | INFO | src.utils.logging_setup | Log file: logs/demo_framework.log
2025-07-13 06:11:58 | INFO | src.framework | Multi-Agent Intent Detection Framework initialized
2025-07-13 06:11:58 | INFO | __main__ | Starting learning phase...
2025-07-13 06:11:58 | INFO | src.framework | Initializing framework components...
2025-07-13 06:11:58 | INFO | src.framework | Initializing LLM client...
2025-07-13 06:11:58 | INFO | src.utils.llm_client | Using GPUs: [0, 1]
2025-07-13 06:11:58 | INFO | src.utils.llm_client | Tensor parallel size: 2
2025-07-13 06:11:58 | INFO | src.utils.llm_client | Loading model: Qwen/Qwen2.5-7B-Instruct
2025-07-13 06:14:47 | ERROR | src.utils.llm_client | Failed to load model: (MaxRetryError("HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/Qwen/Qwen2.5-7B-Instruct/tree/main/additional_chat_templates?recursive=False&expand=False (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>, 'Connection to huggingface.co timed out. (connect timeout=None)'))"), '(Request ID: b6926599-0aab-41ed-81eb-e95b646466bb)')
2025-07-13 06:14:47 | ERROR | src.utils.logging_setup | initialize failed after 169.83 seconds: (MaxRetryError("HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/Qwen/Qwen2.5-7B-Instruct/tree/main/additional_chat_templates?recursive=False&expand=False (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>, 'Connection to huggingface.co timed out. (connect timeout=None)'))"), '(Request ID: b6926599-0aab-41ed-81eb-e95b646466bb)')
2025-07-13 06:14:47 | ERROR | __main__ | Learning demo failed: (MaxRetryError("HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/Qwen/Qwen2.5-7B-Instruct/tree/main/additional_chat_templates?recursive=False&expand=False (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>, 'Connection to huggingface.co timed out. (connect timeout=None)'))"), '(Request ID: b6926599-0aab-41ed-81eb-e95b646466bb)')
2025-07-13 06:14:47 | ERROR | __main__ | Full traceback:
Traceback (most recent call last):

  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           │          └ <function create_connection at 0x7f093fe2df80>
           └ <module 'urllib3.util.connection' from '/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/util/connection.py'>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/util/connection.py", line 85, in create_connection
    raise err
          └ None
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/util/connection.py", line 73, in create_connection
    sock.connect(sa)
    │    │       └ ('128.242.245.125', 443)
    │    └ <method 'connect' of '_socket.socket' objects>
    └ <socket.socket [closed] fd=-1, family=2, type=1, proto=6>

TimeoutError: [Errno 110] Connection timed out


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    response = self._make_request(
               │    └ <function HTTPConnectionPool._make_request at 0x7f093fd11800>
               └ <urllib3.connectionpool.HTTPSConnectionPool object at 0x7f08bd3e57f0>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connectionpool.py", line 488, in _make_request
    raise new_e
          └ ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>, 'Connection to huggingface.co timed out. (...
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
    │    │              └ <urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>
    │    └ <function HTTPSConnectionPool._validate_conn at 0x7f093fd11da0>
    └ <urllib3.connectionpool.HTTPSConnectionPool object at 0x7f08bd3e57f0>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connectionpool.py", line 1093, in _validate_conn
    conn.connect()
    │    └ <function HTTPSConnection.connect at 0x7f093fd098a0>
    └ <urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connection.py", line 753, in connect
    self.sock = sock = self._new_conn()
    │    │             │    └ <function HTTPConnection._new_conn at 0x7f093fd08ea0>
    │    │             └ <urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>
    │    └ None
    └ <urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connection.py", line 207, in _new_conn
    raise ConnectTimeoutError(
          └ <class 'urllib3.exceptions.ConnectTimeoutError'>

urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>, 'Connection to huggingface.co timed out. (connect timeout=None)')


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
           │    └ <function HTTPConnectionPool.urlopen at 0x7f093fd119e0>
           └ <urllib3.connectionpool.HTTPSConnectionPool object at 0x7f08bd3e57f0>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              │       └ <function Retry.increment at 0x7f093fe4c860>
              └ Retry(total=0, connect=None, read=False, redirect=None, status=None)
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/util/retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
          │             │      │    │            └ ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>, 'Connection to huggingface.co timed out. (...
          │             │      │    └ ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>, 'Connection to huggingface.co timed out. (...
          │             │      └ '/api/models/Qwen/Qwen2.5-7B-Instruct/tree/main/additional_chat_templates?recursive=False&expand=False'
          │             └ <urllib3.connectionpool.HTTPSConnectionPool object at 0x7f08bd3e57f0>
          └ <class 'urllib3.exceptions.MaxRetryError'>

urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/Qwen/Qwen2.5-7B-Instruct/tree/main/additional_chat_templates?recursive=False&expand=False (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>, 'Connection to huggingface.co timed out. (connect timeout=None)'))


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "/home/<USER>/exp/L1/demo_run.py", line 213, in <module>
    main()
    └ <function main at 0x7f09221db9c0>

  File "/home/<USER>/exp/L1/demo_run.py", line 201, in main
    success = run_learning_only_demo()
              └ <function run_learning_only_demo at 0x7f094bdb9440>

> File "/home/<USER>/exp/L1/demo_run.py", line 154, in run_learning_only_demo
    framework.initialize()
    │         └ <function log_execution_time.<locals>.wrapper at 0x7f09221db1a0>
    └ <src.framework.MultiAgentIntentDetectionFramework object at 0x7f092210f560>

  File "/home/<USER>/exp/L1/src/utils/logging_setup.py", line 119, in wrapper
    result = func(*args, **kwargs)
             │     │       └ {}
             │     └ (<src.framework.MultiAgentIntentDetectionFramework object at 0x7f092210f560>,)
             └ <function MultiAgentIntentDetectionFramework.initialize at 0x7f08be17c680>

  File "/home/<USER>/exp/L1/src/framework.py", line 55, in initialize
    initialize_llm_client(
    └ <function initialize_llm_client at 0x7f08be1053a0>

  File "/home/<USER>/exp/L1/src/utils/llm_client.py", line 194, in initialize_llm_client
    _llm_client.initialize_model()
    │           └ <function VLLMClient.initialize_model at 0x7f08be105120>
    └ <src.utils.llm_client.VLLMClient object at 0x7f0922135130>

  File "/home/<USER>/exp/L1/src/utils/llm_client.py", line 61, in initialize_model
    self.llm = LLM(
    │    │     └ <class 'vllm.entrypoints.llm.LLM'>
    │    └ None
    └ <src.utils.llm_client.VLLMClient object at 0x7f0922135130>

  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/entrypoints/llm.py", line 178, in __init__
    self.llm_engine = LLMEngine.from_engine_args(
    │                 │         └ <classmethod(<function LLMEngine.from_engine_args at 0x7f08bed079c0>)>
    │                 └ <class 'vllm.engine.llm_engine.LLMEngine'>
    └ <vllm.entrypoints.llm.LLM object at 0x7f09221b6870>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/engine/llm_engine.py", line 550, in from_engine_args
    engine = cls(
             └ <class 'vllm.engine.llm_engine.LLMEngine'>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/engine/llm_engine.py", line 291, in __init__
    self.tokenizer = self._init_tokenizer()
    │                │    └ <function LLMEngine._init_tokenizer at 0x7f08bed07ce0>
    │                └ <vllm.engine.llm_engine.LLMEngine object at 0x7f08bd2b58e0>
    └ <vllm.engine.llm_engine.LLMEngine object at 0x7f08bd2b58e0>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/engine/llm_engine.py", line 594, in _init_tokenizer
    return init_tokenizer_from_configs(
           └ <function init_tokenizer_from_configs at 0x7f08c14f67a0>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/transformers_utils/tokenizer_group/__init__.py", line 28, in init_tokenizer_from_configs
    return get_tokenizer_group(parallel_config.tokenizer_pool_config,
           │                   │               └ None
           │                   └ <vllm.config.ParallelConfig object at 0x7f09221814c0>
           └ <function get_tokenizer_group at 0x7f08bef79440>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/transformers_utils/tokenizer_group/__init__.py", line 49, in get_tokenizer_group
    return tokenizer_cls.from_config(tokenizer_pool_config, **init_kwargs)
           │             │           │                        └ {'tokenizer_id': 'Qwen/Qwen2.5-7B-Instruct', 'enable_lora': False, 'max_num_seqs': 256, 'max_input_length': None, 'tokenizer_...
           │             │           └ None
           │             └ <classmethod(<function TokenizerGroup.from_config at 0x7f08bef79580>)>
           └ <class 'vllm.transformers_utils.tokenizer_group.tokenizer_group.TokenizerGroup'>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/transformers_utils/tokenizer_group/tokenizer_group.py", line 30, in from_config
    return cls(**init_kwargs)
           │     └ {'tokenizer_id': 'Qwen/Qwen2.5-7B-Instruct', 'enable_lora': False, 'max_num_seqs': 256, 'max_input_length': None, 'tokenizer_...
           └ <class 'vllm.transformers_utils.tokenizer_group.tokenizer_group.TokenizerGroup'>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/transformers_utils/tokenizer_group/tokenizer_group.py", line 23, in __init__
    self.tokenizer = get_tokenizer(self.tokenizer_id, **tokenizer_config)
    │                │             │    │               └ {'tokenizer_mode': 'auto', 'trust_remote_code': True, 'revision': None}
    │                │             │    └ 'Qwen/Qwen2.5-7B-Instruct'
    │                │             └ <vllm.transformers_utils.tokenizer_group.tokenizer_group.TokenizerGroup object at 0x7f0922135310>
    │                └ <function get_tokenizer at 0x7f08ca962b60>
    └ <vllm.transformers_utils.tokenizer_group.tokenizer_group.TokenizerGroup object at 0x7f0922135310>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/transformers_utils/tokenizer.py", line 120, in get_tokenizer
    tokenizer = AutoTokenizer.from_pretrained(
                │             └ <classmethod(<function AutoTokenizer.from_pretrained at 0x7f08ca9a8720>)>
                └ <class 'transformers.models.auto.tokenization_auto.AutoTokenizer'>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/transformers/models/auto/tokenization_auto.py", line 1050, in from_pretrained
    return tokenizer_class.from_pretrained(pretrained_model_name_or_path, *inputs, **kwargs)
           │               │               │                               │         └ {'revision': None, 'truncation_side': 'left', '_from_auto': True, '_commit_hash': 'a09a35458c702b33eeacc393d103063234e8bc28'}
           │               │               │                               └ ()
           │               │               └ 'Qwen/Qwen2.5-7B-Instruct'
           │               └ <classmethod(<function PreTrainedTokenizerBase.from_pretrained at 0x7f093eebd440>)>
           └ <class 'transformers.models.qwen2.tokenization_qwen2_fast.Qwen2TokenizerFast'>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/transformers/tokenization_utils_base.py", line 1957, in from_pretrained
    for template in list_repo_templates(
                    └ <function list_repo_templates at 0x7f093f5cfc40>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/transformers/utils/hub.py", line 163, in list_repo_templates
    for entry in list_repo_tree(
                 └ <bound method HfApi.list_repo_tree of <huggingface_hub.hf_api.HfApi object at 0x7f093f9ac800>>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/huggingface_hub/hf_api.py", line 3168, in list_repo_tree
    for path_info in paginate(path=tree_url, headers=headers, params={"recursive": recursive, "expand": expand}):
                     │             │                 │                             │                    └ False
                     │             │                 │                             └ False
                     │             │                 └ {'user-agent': 'unknown/None; hf_hub/0.33.4; python/3.12.11; torch/2.4.0+cu118'}
                     │             └ 'https://huggingface.co/api/models/Qwen/Qwen2.5-7B-Instruct/tree/main/additional_chat_templates'
                     └ <function paginate at 0x7f093fc03c40>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/huggingface_hub/utils/_pagination.py", line 36, in paginate
    r = session.get(path, params=params, headers=headers)
        │       │   │            │               └ {'user-agent': 'unknown/None; hf_hub/0.33.4; python/3.12.11; torch/2.4.0+cu118'}
        │       │   │            └ {'recursive': False, 'expand': False}
        │       │   └ 'https://huggingface.co/api/models/Qwen/Qwen2.5-7B-Instruct/tree/main/additional_chat_templates'
        │       └ <function Session.get at 0x7f093fbf7740>
        └ <requests.sessions.Session object at 0x7f092210c170>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/requests/sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           │    │              │      └ {'params': {'recursive': False, 'expand': False}, 'headers': {'user-agent': 'unknown/None; hf_hub/0.33.4; python/3.12.11; tor...
           │    │              └ 'https://huggingface.co/api/models/Qwen/Qwen2.5-7B-Instruct/tree/main/additional_chat_templates'
           │    └ <function Session.request at 0x7f093fbf76a0>
           └ <requests.sessions.Session object at 0x7f092210c170>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           │    │    │       └ {'timeout': None, 'allow_redirects': True, 'proxies': OrderedDict(), 'stream': False, 'verify': True, 'cert': None}
           │    │    └ <PreparedRequest [GET]>
           │    └ <function Session.send at 0x7f093fbf7ba0>
           └ <requests.sessions.Session object at 0x7f092210c170>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        │       │    │          └ {'timeout': None, 'proxies': OrderedDict(), 'stream': False, 'verify': True, 'cert': None}
        │       │    └ <PreparedRequest [GET]>
        │       └ <function UniqueRequestIdAdapter.send at 0x7f093fad8180>
        └ <huggingface_hub.utils._http.UniqueRequestIdAdapter object at 0x7f0921fb88f0>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/huggingface_hub/utils/_http.py", line 96, in send
    return super().send(request, *args, **kwargs)
                        │         │       └ {'timeout': None, 'proxies': OrderedDict(), 'stream': False, 'verify': True, 'cert': None}
                        │         └ ()
                        └ <PreparedRequest [GET]>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/requests/adapters.py", line 688, in send
    raise ConnectTimeout(e, request=request)
          │                         └ <PreparedRequest [GET]>
          └ <class 'requests.exceptions.ConnectTimeout'>

requests.exceptions.ConnectTimeout: (MaxRetryError("HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/Qwen/Qwen2.5-7B-Instruct/tree/main/additional_chat_templates?recursive=False&expand=False (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7f0921fb8830>, 'Connection to huggingface.co timed out. (connect timeout=None)'))"), '(Request ID: b6926599-0aab-41ed-81eb-e95b646466bb)')
2025-07-13 06:14:48 | ERROR | __main__ | ❌ Demo failed!
2025-07-13 06:18:25 | INFO | src.utils.logging_setup | Logging setup completed
2025-07-13 06:18:25 | INFO | src.utils.logging_setup | Log level: INFO
2025-07-13 06:18:25 | INFO | src.utils.logging_setup | Log file: logs/demo_framework.log
2025-07-13 06:18:25 | INFO | src.framework | Multi-Agent Intent Detection Framework initialized
2025-07-13 06:18:25 | INFO | __main__ | Starting learning phase...
2025-07-13 06:18:25 | INFO | src.framework | Initializing framework components...
2025-07-13 06:18:25 | INFO | src.framework | Initializing LLM client...
2025-07-13 06:18:25 | INFO | src.utils.llm_client | Using HuggingFace mirror: https://hf-mirror.com
2025-07-13 06:18:25 | INFO | src.utils.llm_client | Using GPUs: [0, 1]
2025-07-13 06:18:25 | INFO | src.utils.llm_client | Tensor parallel size: 2
2025-07-13 06:18:25 | INFO | src.utils.llm_client | Loading model: Qwen/Qwen2.5-7B-Instruct
2025-07-13 06:18:29 | ERROR | src.utils.llm_client | Failed to load model: worker died
2025-07-13 06:18:29 | ERROR | src.utils.logging_setup | initialize failed after 3.54 seconds: worker died
2025-07-13 06:18:29 | ERROR | __main__ | Learning demo failed: worker died
2025-07-13 06:18:29 | ERROR | __main__ | Full traceback:
Traceback (most recent call last):

  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/executor/multiproc_worker_utils.py", line 169, in _enqueue_task
    self._task_queue.put((task_id, method, args, kwargs))
    │    │           │    │        │       │     └ {}
    │    │           │    │        │       └ ()
    │    │           │    │        └ 'init_device'
    │    │           │    └ UUID('4bca53c9-6292-4621-97ae-ad864176ab93')
    │    │           └ <function Queue.put at 0x7f71dc8cdb20>
    │    └ <multiprocessing.queues.Queue object at 0x7f72322ec050>
    └ <vllm.executor.multiproc_worker_utils.ProcessWorkerWrapper object at 0x7f72322ec920>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/multiprocessing/queues.py", line 88, in put
    raise ValueError(f"Queue {self!r} is closed")
                              └ <multiprocessing.queues.Queue object at 0x7f72322ec050>

ValueError: Queue <multiprocessing.queues.Queue object at 0x7f72322ec050> is closed


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "/home/<USER>/exp/L1/demo_china.py", line 224, in <module>
    main()
    └ <function main at 0x7f72324fba60>

  File "/home/<USER>/exp/L1/demo_china.py", line 212, in main
    success = run_learning_demo()
              └ <function run_learning_demo at 0x7f725bfc93a0>

> File "/home/<USER>/exp/L1/demo_china.py", line 88, in run_learning_demo
    framework.initialize()
    │         └ <function log_execution_time.<locals>.wrapper at 0x7f72324fb240>
    └ <src.framework.MultiAgentIntentDetectionFramework object at 0x7f71cd7bde50>

  File "/home/<USER>/exp/L1/src/utils/logging_setup.py", line 119, in wrapper
    result = func(*args, **kwargs)
             │     │       └ {}
             │     └ (<src.framework.MultiAgentIntentDetectionFramework object at 0x7f71cd7bde50>,)
             └ <function MultiAgentIntentDetectionFramework.initialize at 0x7f71ce488720>

  File "/home/<USER>/exp/L1/src/framework.py", line 55, in initialize
    initialize_llm_client(
    └ <function initialize_llm_client at 0x7f71ce4114e0>

  File "/home/<USER>/exp/L1/src/utils/llm_client.py", line 199, in initialize_llm_client
    _llm_client.initialize_model()
    │           └ <function VLLMClient.initialize_model at 0x7f71ce411260>
    └ <src.utils.llm_client.VLLMClient object at 0x7f72324ea150>

  File "/home/<USER>/exp/L1/src/utils/llm_client.py", line 66, in initialize_model
    self.llm = LLM(
    │    │     └ <class 'vllm.entrypoints.llm.LLM'>
    │    └ None
    └ <src.utils.llm_client.VLLMClient object at 0x7f72324ea150>

  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/entrypoints/llm.py", line 178, in __init__
    self.llm_engine = LLMEngine.from_engine_args(
    │                 │         └ <classmethod(<function LLMEngine.from_engine_args at 0x7f71cf013b00>)>
    │                 └ <class 'vllm.engine.llm_engine.LLMEngine'>
    └ <vllm.entrypoints.llm.LLM object at 0x7f72324ea510>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/engine/llm_engine.py", line 550, in from_engine_args
    engine = cls(
             └ <class 'vllm.engine.llm_engine.LLMEngine'>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/engine/llm_engine.py", line 317, in __init__
    self.model_executor = executor_class(
    │                     └ <class 'vllm.executor.multiproc_gpu_executor.MultiprocessingGPUExecutor'>
    └ <vllm.engine.llm_engine.LLMEngine object at 0x7f71cd64b530>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/executor/distributed_gpu_executor.py", line 26, in __init__
    super().__init__(*args, **kwargs)
                      │       └ {'model_config': <vllm.config.ModelConfig object at 0x7f72324a3680>, 'cache_config': <vllm.config.CacheConfig object at 0x7f7...
                      └ ()
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/executor/executor_base.py", line 47, in __init__
    self._init_executor()
    │    └ <function MultiprocessingGPUExecutor._init_executor at 0x7f72322daca0>
    └ <vllm.executor.multiproc_gpu_executor.MultiprocessingGPUExecutor object at 0x7f72324ebc20>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/executor/multiproc_gpu_executor.py", line 124, in _init_executor
    self._run_workers("init_device")
    │    └ <function MultiprocessingGPUExecutor._run_workers at 0x7f72322dbd80>
    └ <vllm.executor.multiproc_gpu_executor.MultiprocessingGPUExecutor object at 0x7f72324ebc20>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/executor/multiproc_gpu_executor.py", line 194, in _run_workers
    worker.execute_method(method, *args, **kwargs)
                          │        │       └ {}
                          │        └ ()
                          └ 'init_device'
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/executor/multiproc_worker_utils.py", line 176, in execute_method
    self._enqueue_task(future, method, args, kwargs)
    │    │             │       │       │     └ {}
    │    │             │       │       └ ()
    │    │             │       └ 'init_device'
    │    │             └ <vllm.executor.multiproc_worker_utils.ResultFuture at 0x7f72324ea4e0: unset>
    │    └ <function ProcessWorkerWrapper._enqueue_task at 0x7f72322db880>
    └ <vllm.executor.multiproc_worker_utils.ProcessWorkerWrapper object at 0x7f72322ec920>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/executor/multiproc_worker_utils.py", line 172, in _enqueue_task
    raise ChildProcessError("worker died") from e

ChildProcessError: worker died
2025-07-13 06:18:29 | ERROR | __main__ | ❌ Demo failed!
2025-07-13 06:19:32 | INFO | src.utils.logging_setup | Logging setup completed
2025-07-13 06:19:32 | INFO | src.utils.logging_setup | Log level: INFO
2025-07-13 06:19:32 | INFO | src.utils.logging_setup | Log file: logs/demo_framework.log
2025-07-13 06:19:32 | INFO | src.framework | Multi-Agent Intent Detection Framework initialized
2025-07-13 06:19:32 | INFO | __main__ | Starting learning phase...
2025-07-13 06:19:32 | INFO | src.framework | Initializing framework components...
2025-07-13 06:19:32 | INFO | src.framework | Initializing LLM client...
2025-07-13 06:19:32 | INFO | src.utils.llm_client | Using HuggingFace mirror: https://hf-mirror.com
2025-07-13 06:19:32 | INFO | src.utils.llm_client | Using GPUs: [0]
2025-07-13 06:19:32 | INFO | src.utils.llm_client | Tensor parallel size: 1
2025-07-13 06:19:32 | INFO | src.utils.llm_client | Loading model: Qwen/Qwen2.5-7B-Instruct
2025-07-13 06:21:50 | ERROR | src.utils.llm_client | Failed to load model: (MaxRetryError("HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/Qwen/Qwen2.5-7B-Instruct/xet-read-token/a09a35458c702b33eeacc393d103063234e8bc28 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7fb5f60ba060>, 'Connection to huggingface.co timed out. (connect timeout=None)'))"), '(Request ID: e164c399-ee0b-49fc-af74-89ea428fefe6)')
2025-07-13 06:21:50 | ERROR | src.utils.logging_setup | initialize failed after 137.72 seconds: (MaxRetryError("HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/Qwen/Qwen2.5-7B-Instruct/xet-read-token/a09a35458c702b33eeacc393d103063234e8bc28 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7fb5f60ba060>, 'Connection to huggingface.co timed out. (connect timeout=None)'))"), '(Request ID: e164c399-ee0b-49fc-af74-89ea428fefe6)')
2025-07-13 06:21:50 | ERROR | __main__ | Learning demo failed: (MaxRetryError("HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/Qwen/Qwen2.5-7B-Instruct/xet-read-token/a09a35458c702b33eeacc393d103063234e8bc28 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7fb5f60ba060>, 'Connection to huggingface.co timed out. (connect timeout=None)'))"), '(Request ID: e164c399-ee0b-49fc-af74-89ea428fefe6)')
2025-07-13 06:21:50 | ERROR | __main__ | Full traceback:
Traceback (most recent call last):

  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           │          └ <function create_connection at 0x7fb686a31ee0>
           └ <module 'urllib3.util.connection' from '/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/util/connection.py'>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/util/connection.py", line 85, in create_connection
    raise err
          └ None
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/util/connection.py", line 73, in create_connection
    sock.connect(sa)
    │    │       └ ('157.240.12.36', 443)
    │    └ <method 'connect' of '_socket.socket' objects>
    └ <socket.socket [closed] fd=-1, family=2, type=1, proto=6>

TimeoutError: [Errno 110] Connection timed out


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    response = self._make_request(
               │    └ <function HTTPConnectionPool._make_request at 0x7fb68691d760>
               └ <urllib3.connectionpool.HTTPSConnectionPool object at 0x7fb5f60b9d30>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connectionpool.py", line 488, in _make_request
    raise new_e
          └ ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7fb5f60ba060>, 'Connection to huggingface.co timed out. (...
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
    │    │              └ <urllib3.connection.HTTPSConnection object at 0x7fb5f60ba060>
    │    └ <function HTTPSConnectionPool._validate_conn at 0x7fb68691dd00>
    └ <urllib3.connectionpool.HTTPSConnectionPool object at 0x7fb5f60b9d30>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connectionpool.py", line 1093, in _validate_conn
    conn.connect()
    │    └ <function HTTPSConnection.connect at 0x7fb686915800>
    └ <urllib3.connection.HTTPSConnection object at 0x7fb5f60ba060>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connection.py", line 753, in connect
    self.sock = sock = self._new_conn()
    │    │             │    └ <function HTTPConnection._new_conn at 0x7fb686914e00>
    │    │             └ <urllib3.connection.HTTPSConnection object at 0x7fb5f60ba060>
    │    └ None
    └ <urllib3.connection.HTTPSConnection object at 0x7fb5f60ba060>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connection.py", line 207, in _new_conn
    raise ConnectTimeoutError(
          └ <class 'urllib3.exceptions.ConnectTimeoutError'>

urllib3.exceptions.ConnectTimeoutError: (<urllib3.connection.HTTPSConnection object at 0x7fb5f60ba060>, 'Connection to huggingface.co timed out. (connect timeout=None)')


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
           │    └ <function HTTPConnectionPool.urlopen at 0x7fb68691d940>
           └ <urllib3.connectionpool.HTTPSConnectionPool object at 0x7fb5f60b9d30>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              │       └ <function Retry.increment at 0x7fb686a547c0>
              └ Retry(total=0, connect=None, read=False, redirect=None, status=None)
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/urllib3/util/retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
          │             │      │    │            └ ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7fb5f60ba060>, 'Connection to huggingface.co timed out. (...
          │             │      │    └ ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7fb5f60ba060>, 'Connection to huggingface.co timed out. (...
          │             │      └ '/api/models/Qwen/Qwen2.5-7B-Instruct/xet-read-token/a09a35458c702b33eeacc393d103063234e8bc28'
          │             └ <urllib3.connectionpool.HTTPSConnectionPool object at 0x7fb5f60b9d30>
          └ <class 'urllib3.exceptions.MaxRetryError'>

urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/Qwen/Qwen2.5-7B-Instruct/xet-read-token/a09a35458c702b33eeacc393d103063234e8bc28 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7fb5f60ba060>, 'Connection to huggingface.co timed out. (connect timeout=None)'))


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "/home/<USER>/exp/L1/demo_china.py", line 224, in <module>
    main()
    └ <function main at 0x7fb668dc3920>

  File "/home/<USER>/exp/L1/demo_china.py", line 212, in main
    success = run_learning_demo()
              └ <function run_learning_demo at 0x7fb6929c53a0>

> File "/home/<USER>/exp/L1/demo_china.py", line 88, in run_learning_demo
    framework.initialize()
    │         └ <function log_execution_time.<locals>.wrapper at 0x7fb668dc3100>
    └ <src.framework.MultiAgentIntentDetectionFramework object at 0x7fb603f41df0>

  File "/home/<USER>/exp/L1/src/utils/logging_setup.py", line 119, in wrapper
    result = func(*args, **kwargs)
             │     │       └ {}
             │     └ (<src.framework.MultiAgentIntentDetectionFramework object at 0x7fb603f41df0>,)
             └ <function MultiAgentIntentDetectionFramework.initialize at 0x7fb604d305e0>

  File "/home/<USER>/exp/L1/src/framework.py", line 55, in initialize
    initialize_llm_client(
    └ <function initialize_llm_client at 0x7fb604cc1300>

  File "/home/<USER>/exp/L1/src/utils/llm_client.py", line 199, in initialize_llm_client
    _llm_client.initialize_model()
    │           └ <function VLLMClient.initialize_model at 0x7fb604cc1080>
    └ <src.utils.llm_client.VLLMClient object at 0x7fb668d6df70>

  File "/home/<USER>/exp/L1/src/utils/llm_client.py", line 66, in initialize_model
    self.llm = LLM(
    │    │     └ <class 'vllm.entrypoints.llm.LLM'>
    │    └ None
    └ <src.utils.llm_client.VLLMClient object at 0x7fb668d6df70>

  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/entrypoints/llm.py", line 178, in __init__
    self.llm_engine = LLMEngine.from_engine_args(
    │                 │         └ <classmethod(<function LLMEngine.from_engine_args at 0x7fb6058ef920>)>
    │                 └ <class 'vllm.engine.llm_engine.LLMEngine'>
    └ <vllm.entrypoints.llm.LLM object at 0x7fb603e334d0>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/engine/llm_engine.py", line 550, in from_engine_args
    engine = cls(
             └ <class 'vllm.engine.llm_engine.LLMEngine'>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/engine/llm_engine.py", line 317, in __init__
    self.model_executor = executor_class(
    │                     └ <class 'vllm.executor.gpu_executor.GPUExecutor'>
    └ <vllm.engine.llm_engine.LLMEngine object at 0x7fb6040f02c0>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/executor/executor_base.py", line 47, in __init__
    self._init_executor()
    │    └ <function GPUExecutor._init_executor at 0x7fb605b76ac0>
    └ <vllm.executor.gpu_executor.GPUExecutor object at 0x7fb603e33560>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/executor/gpu_executor.py", line 40, in _init_executor
    self.driver_worker.load_model()
    │    │             └ <function Worker.load_model at 0x7fb5fcd013a0>
    │    └ <vllm.worker.worker.Worker object at 0x7fb603f426c0>
    └ <vllm.executor.gpu_executor.GPUExecutor object at 0x7fb603e33560>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/worker/worker.py", line 183, in load_model
    self.model_runner.load_model()
    │    │            └ <function GPUModelRunnerBase.load_model at 0x7fb5fccda020>
    │    └ <vllm.worker.model_runner.ModelRunner object at 0x7fb603e82150>
    └ <vllm.worker.worker.Worker object at 0x7fb603f426c0>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/worker/model_runner.py", line 999, in load_model
    self.model = get_model(model_config=self.model_config,
    │            │                      │    └ <vllm.config.ModelConfig object at 0x7fb668d6f230>
    │            │                      └ <vllm.worker.model_runner.ModelRunner object at 0x7fb603e82150>
    │            └ <function get_model at 0x7fb668bf4b80>
    └ <vllm.worker.model_runner.ModelRunner object at 0x7fb603e82150>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/model_executor/model_loader/__init__.py", line 19, in get_model
    return loader.load_model(model_config=model_config,
           │      │                       └ <vllm.config.ModelConfig object at 0x7fb668d6f230>
           │      └ <function DefaultModelLoader.load_model at 0x7fb5fce00540>
           └ <vllm.model_executor.model_loader.loader.DefaultModelLoader object at 0x7fb5fc9d3710>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/model_executor/model_loader/loader.py", line 362, in load_model
    self._get_weights_iterator(model_config.model,
    │    │                     │            └ 'Qwen/Qwen2.5-7B-Instruct'
    │    │                     └ <vllm.config.ModelConfig object at 0x7fb668d6f230>
    │    └ <function DefaultModelLoader._get_weights_iterator at 0x7fb5fce00360>
    └ <vllm.model_executor.model_loader.loader.DefaultModelLoader object at 0x7fb5fc9d3710>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/model_executor/model_loader/loader.py", line 318, in _get_weights_iterator
    hf_folder, hf_weights_files, use_safetensors = self._prepare_weights(
                                                   │    └ <function DefaultModelLoader._prepare_weights at 0x7fb5fce002c0>
                                                   └ <vllm.model_executor.model_loader.loader.DefaultModelLoader object at 0x7fb5fc9d3710>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/model_executor/model_loader/loader.py", line 273, in _prepare_weights
    hf_folder = download_weights_from_hf(
                └ <function download_weights_from_hf at 0x7fb5fcde2700>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/vllm/model_executor/model_loader/weight_utils.py", line 246, in download_weights_from_hf
    hf_folder = snapshot_download(
                └ <function snapshot_download at 0x7fb6861bfc40>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           │   │       └ {'allow_patterns': ['*.safetensors'], 'ignore_patterns': ['original/**/*'], 'cache_dir': None, 'tqdm_class': <class 'vllm.mod...
           │   └ ('Qwen/Qwen2.5-7B-Instruct',)
           └ <function snapshot_download at 0x7fb6861bfa60>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/huggingface_hub/_snapshot_download.py", line 327, in snapshot_download
    thread_map(
    └ <function thread_map at 0x7fb6867f9260>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/tqdm/contrib/concurrent.py", line 69, in thread_map
    return _executor_map(ThreadPoolExecutor, fn, *iterables, **tqdm_kwargs)
           │             │                   │    │            └ {'desc': 'Fetching 4 files', 'max_workers': 8, 'tqdm_class': <class 'vllm.model_executor.model_loader.weight_utils.DisabledTq...
           │             │                   │    └ (['model-00001-of-00004.safetensors', 'model-00002-of-00004.safetensors', 'model-00003-of-00004.safetensors', 'model-00004-of...
           │             │                   └ <function snapshot_download.<locals>._inner_hf_hub_download at 0x7fb5fca8cea0>
           │             └ <class 'concurrent.futures.thread.ThreadPoolExecutor'>
           └ <function _executor_map at 0x7fb6867f91c0>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/tqdm/contrib/concurrent.py", line 51, in _executor_map
    return list(tqdm_class(ex.map(fn, *iterables, chunksize=chunksize), **kwargs))
                │          │  │   │    │                    │             └ {'desc': 'Fetching 4 files', 'total': 4}
                │          │  │   │    │                    └ 1
                │          │  │   │    └ (['model-00001-of-00004.safetensors', 'model-00002-of-00004.safetensors', 'model-00003-of-00004.safetensors', 'model-00004-of...
                │          │  │   └ <function snapshot_download.<locals>._inner_hf_hub_download at 0x7fb5fca8cea0>
                │          │  └ <function Executor.map at 0x7fb6938e3920>
                │          └ <concurrent.futures.thread.ThreadPoolExecutor object at 0x7fb5f6071d30>
                └ <class 'vllm.model_executor.model_loader.weight_utils.DisabledTqdm'>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/tqdm/std.py", line 1169, in __iter__
    for obj in iterable:
               └ <generator object Executor.map.<locals>.result_iterator at 0x7fb5f60b4b80>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/concurrent/futures/_base.py", line 619, in result_iterator
    yield _result_or_cancel(fs.pop())
          │                 │  └ <method 'pop' of 'list' objects>
          │                 └ [<Future at 0x7fb5f6073f50 state=finished returned str>, <Future at 0x7fb5f6073380 state=finished returned str>, <Future at 0...
          └ <function _result_or_cancel at 0x7fb6938e2de0>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/concurrent/futures/_base.py", line 317, in _result_or_cancel
    return fut.result(timeout)
                      └ None
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/concurrent/futures/_base.py", line 456, in result
    return self.__get_result()
           └ None
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
          └ None
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/concurrent/futures/thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/huggingface_hub/_snapshot_download.py", line 301, in _inner_hf_hub_download
    return hf_hub_download(
           └ <function hf_hub_download at 0x7fb6866f0d60>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           │   │       └ {'filename': 'model-00001-of-00004.safetensors', 'repo_type': 'model', 'revision': 'a09a35458c702b33eeacc393d103063234e8bc28'...
           │   └ ('Qwen/Qwen2.5-7B-Instruct',)
           └ <function hf_hub_download at 0x7fb6866f0c20>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/huggingface_hub/file_download.py", line 1008, in hf_hub_download
    return _hf_hub_download_to_cache_dir(
           └ <function _hf_hub_download_to_cache_dir at 0x7fb6866f0cc0>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/huggingface_hub/file_download.py", line 1161, in _hf_hub_download_to_cache_dir
    _download_to_tmp_and_move(
    └ <function _download_to_tmp_and_move at 0x7fb6866f1300>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/huggingface_hub/file_download.py", line 1710, in _download_to_tmp_and_move
    xet_get(
    └ <function xet_get at 0x7fb6866f0720>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/huggingface_hub/file_download.py", line 592, in xet_get
    connection_info = refresh_xet_connection_info(file_data=xet_file_data, headers=headers)
                      │                                     │                      └ {'user-agent': 'unknown/None; hf_hub/0.33.4; python/3.12.11'}
                      │                                     └ XetFileData(file_hash='ddfa1ff25e39c57076e10f1a8b02cc96888ce69b0717f0d296c57b842695c965', refresh_route='https://huggingface....
                      └ <function refresh_xet_connection_info at 0x7fb6866cac00>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           │   │       └ {'file_data': XetFileData(file_hash='ddfa1ff25e39c57076e10f1a8b02cc96888ce69b0717f0d296c57b842695c965', refresh_route='https:...
           │   └ ()
           └ <function refresh_xet_connection_info at 0x7fb6866cab60>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/huggingface_hub/utils/_xet.py", line 112, in refresh_xet_connection_info
    return _fetch_xet_connection_info_with_url(file_data.refresh_route, headers)
           │                                   │         │              └ {'user-agent': 'unknown/None; hf_hub/0.33.4; python/3.12.11'}
           │                                   │         └ 'https://huggingface.co/api/models/Qwen/Qwen2.5-7B-Instruct/xet-read-token/a09a35458c702b33eeacc393d103063234e8bc28'
           │                                   └ XetFileData(file_hash='ddfa1ff25e39c57076e10f1a8b02cc96888ce69b0717f0d296c57b842695c965', refresh_route='https://huggingface....
           └ <function _fetch_xet_connection_info_with_url at 0x7fb6866cae80>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           │   │       └ {}
           │   └ ('https://huggingface.co/api/models/Qwen/Qwen2.5-7B-Instruct/xet-read-token/a09a35458c702b33eeacc393d103063234e8bc28', {'user...
           └ <function _fetch_xet_connection_info_with_url at 0x7fb6866caca0>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/huggingface_hub/utils/_xet.py", line 182, in _fetch_xet_connection_info_with_url
    resp = get_session().get(headers=headers, url=url, params=params)
           │                         │            │           └ None
           │                         │            └ 'https://huggingface.co/api/models/Qwen/Qwen2.5-7B-Instruct/xet-read-token/a09a35458c702b33eeacc393d103063234e8bc28'
           │                         └ {'user-agent': 'unknown/None; hf_hub/0.33.4; python/3.12.11'}
           └ <function get_session at 0x7fb6866c8900>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/requests/sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           │    │              │      └ {'headers': {'user-agent': 'unknown/None; hf_hub/0.33.4; python/3.12.11'}, 'params': None, 'allow_redirects': True}
           │    │              └ 'https://huggingface.co/api/models/Qwen/Qwen2.5-7B-Instruct/xet-read-token/a09a35458c702b33eeacc393d103063234e8bc28'
           │    └ <function Session.request at 0x7fb6867ef600>
           └ <requests.sessions.Session object at 0x7fb5f6072ae0>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           │    │    │       └ {'timeout': None, 'allow_redirects': True, 'proxies': OrderedDict(), 'stream': False, 'verify': True, 'cert': None}
           │    │    └ <PreparedRequest [GET]>
           │    └ <function Session.send at 0x7fb6867efb00>
           └ <requests.sessions.Session object at 0x7fb5f6072ae0>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        │       │    │          └ {'timeout': None, 'proxies': OrderedDict(), 'stream': False, 'verify': True, 'cert': None}
        │       │    └ <PreparedRequest [GET]>
        │       └ <function UniqueRequestIdAdapter.send at 0x7fb6866c80e0>
        └ <huggingface_hub.utils._http.UniqueRequestIdAdapter object at 0x7fb5f6072c30>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/huggingface_hub/utils/_http.py", line 96, in send
    return super().send(request, *args, **kwargs)
                        │         │       └ {'timeout': None, 'proxies': OrderedDict(), 'stream': False, 'verify': True, 'cert': None}
                        │         └ ()
                        └ <PreparedRequest [GET]>
  File "/home/<USER>/anaconda3/envs/LR/lib/python3.12/site-packages/requests/adapters.py", line 688, in send
    raise ConnectTimeout(e, request=request)
          │                         └ <PreparedRequest [GET]>
          └ <class 'requests.exceptions.ConnectTimeout'>

requests.exceptions.ConnectTimeout: (MaxRetryError("HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/Qwen/Qwen2.5-7B-Instruct/xet-read-token/a09a35458c702b33eeacc393d103063234e8bc28 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7fb5f60ba060>, 'Connection to huggingface.co timed out. (connect timeout=None)'))"), '(Request ID: e164c399-ee0b-49fc-af74-89ea428fefe6)')
2025-07-13 06:21:50 | ERROR | __main__ | ❌ Demo failed!
