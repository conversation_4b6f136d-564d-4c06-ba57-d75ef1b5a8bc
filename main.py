#!/usr/bin/env python3
"""
Main entry point for the Multi-Agent Intent Detection Framework
"""
import argparse
import sys
from pathlib import Path
import torch
from loguru import logger

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.framework import MultiAgentIntentDetectionFramework
from src.utils.config import load_config
from src.utils.logging_setup import setup_logging


def check_gpu_availability():
    """Check GPU availability and configuration"""
    
    if not torch.cuda.is_available():
        logger.error("CUDA is not available. This framework requires GPU support.")
        return False
        
    num_gpus = torch.cuda.device_count()
    logger.info(f"Found {num_gpus} GPU(s)")
    
    for i in range(num_gpus):
        gpu_name = torch.cuda.get_device_name(i)
        gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
        logger.info(f"GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
        
    if num_gpus < 2:
        logger.warning("Framework is configured for 2 GPUs, but only {} available".format(num_gpus))
        logger.warning("Performance may be reduced")
        
    return True


def main():
    """Main function"""
    
    parser = argparse.ArgumentParser(
        description="Multi-Agent Intent Detection Framework",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                           # Run with default config
  python main.py --config custom.yaml     # Run with custom config
  python main.py --learning-only          # Run only learning phase
  python main.py --testing-only           # Run only testing phase (requires existing principles)
        """
    )
    
    parser.add_argument(
        "--config",
        type=str,
        default="config.yaml",
        help="Path to configuration file (default: config.yaml)"
    )
    
    parser.add_argument(
        "--learning-only",
        action="store_true",
        help="Run only the learning phase"
    )
    
    parser.add_argument(
        "--testing-only",
        action="store_true",
        help="Run only the testing phase (requires existing principles)"
    )
    
    parser.add_argument(
        "--principles-file",
        type=str,
        help="Path to principles file for testing-only mode"
    )
    
    parser.add_argument(
        "--output-dir",
        type=str,
        help="Override output directory"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose logging"
    )
    
    parser.add_argument(
        "--gpu-check",
        action="store_true",
        help="Check GPU availability and exit"
    )
    
    args = parser.parse_args()
    
    # Check GPU availability if requested
    if args.gpu_check:
        check_gpu_availability()
        return
        
    try:
        # Load configuration
        config = load_config(args.config)
        
        # Override config with command line arguments
        if args.output_dir:
            config.framework.output_dir = args.output_dir
            
        if args.verbose:
            config.logging.level = "DEBUG"
            
        # Setup logging
        setup_logging(config.logging)
        
        logger.info("="*60)
        logger.info("Multi-Agent Intent Detection Framework")
        logger.info("="*60)
        
        # Check GPU availability
        if not check_gpu_availability():
            logger.error("GPU check failed. Exiting.")
            sys.exit(1)
            
        # Initialize framework
        framework = MultiAgentIntentDetectionFramework(args.config)
        
        if args.learning_only:
            # Run only learning phase
            logger.info("Running learning phase only...")
            framework.initialize()
            principles = framework.run_learning_phase()
            logger.info(f"Learning completed. Generated {len(principles)} principles.")
            
        elif args.testing_only:
            # Run only testing phase
            if not args.principles_file:
                logger.error("Testing-only mode requires --principles-file argument")
                sys.exit(1)
                
            logger.info("Running testing phase only...")
            framework.initialize()
            
            # Load principles from file
            import json
            with open(args.principles_file, 'r') as f:
                principles_data = json.load(f)
                
            # Convert to Principle objects
            from src.agents.base_agent import Principle
            principles = [
                Principle(
                    id=p['id'],
                    content=p['content'],
                    confidence=p.get('confidence', 1.0),
                    source_agent=p.get('source_agent', 'unknown')
                )
                for p in principles_data
            ]
            
            results = framework.run_testing_phase(principles)
            logger.info("Testing completed.")
            
        else:
            # Run full pipeline
            logger.info("Running full pipeline...")
            results = framework.run_full_pipeline()
            
            # Print summary
            testing_results = results.get('testing_phase', {})
            if 'overall_metrics' in testing_results:
                metrics = testing_results['overall_metrics']
                logger.info("="*60)
                logger.info("FINAL RESULTS SUMMARY")
                logger.info("="*60)
                logger.info(f"Accuracy:    {metrics.get('accuracy', 0):.4f}")
                logger.info(f"F1 Macro:    {metrics.get('f1_macro', 0):.4f}")
                logger.info(f"F1 Micro:    {metrics.get('f1_micro', 0):.4f}")
                logger.info(f"Precision:   {metrics.get('precision_macro', 0):.4f}")
                logger.info(f"Recall:      {metrics.get('recall_macro', 0):.4f}")
                logger.info("="*60)
                
        logger.info("Framework execution completed successfully!")
        
    except KeyboardInterrupt:
        logger.info("Execution interrupted by user")
        sys.exit(1)
        
    except Exception as e:
        logger.error(f"Framework execution failed: {e}")
        logger.exception("Full traceback:")
        sys.exit(1)


if __name__ == "__main__":
    main()
