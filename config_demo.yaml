# Demo Configuration for Multi-Agent Intent Detection Framework (100 samples)

# Model Configuration
model:
  name: "Qwen/Qwen2.5-7B-Instruct"
  hf_mirror: "https://hf-mirror.com"  # China mainland mirror
  hf_endpoint: "https://hf-mirror.com"  # Alternative endpoint
  temperature: 0.2
  top_p: 0.9
  max_tokens: 1024  # Reduced for faster inference
  gpu_devices: [0]  # Use single GPU for demo
  tensor_parallel_size: 1
  gpu_memory_utilization: 0.75  # 稍微降低显存使用率
  max_model_len: 4096  # 增加到4096支持更长序列
  dtype: "half"
  offline_mode: false  # Set to true if model is already downloaded

# Data Configuration
data:
  train_path: "stackoverflow/train.tsv"
  test_path: "stackoverflow/test.tsv"
  dev_path: "stackoverflow/dev.tsv"
  intent_labels: [
    "wordpress", "oracle", "svn", "apache", "excel", "matlab",
    "visual-studio", "cocoa", "osx", "bash", "spring", "hibernate",
    "scala", "sharepoint", "ajax", "qt", "drupal", "linq", "haskell", "magento"
  ]
  examples_per_intent: 1  # Number of examples to show in prompts
  max_text_length: 256  # Reduced for faster processing
  demo_test_samples: 100  # Use 100 test samples for evaluation

# Agent Configuration
agents:
  student:
    count: 4
    strategies: ["analytical", "pattern_based", "linguistic", "contextual"]
    max_principles: 5  # 5 principles per agent

  teacher:
    evaluation_dimensions: 2
    dimension_weights: [0.5, 0.5]  # [adequacy, clarity]
    quality_threshold: 0.7  # Standard threshold
    consolidation_strategy: "weighted_synthesis"

  exam:
    batch_size: 10  # Batch size for classification
    evaluation_metrics: ["accuracy", "f1_macro", "f1_micro", "precision", "recall"]

# Framework Configuration
framework:
  max_iterations: 1  # Single iteration
  convergence_threshold: 0.95
  parallel_processing: false  # Sequential for stability
  save_intermediate_results: true
  output_dir: "full_framework_results"

# Logging Configuration
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name} | {message}"
  file: "logs/demo_framework.log"
  rotation: "1 day"
  retention: "7 days"
