#!/usr/bin/env python3
"""
使用Ollama部署Qwen2.5-7B的多智能体意图检测框架演示
"""
import os
import sys
import time
from pathlib import Path
from loguru import logger

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.config import load_config
from src.utils.data_processor import create_data_processor
from src.utils.ollama_client import (
    initialize_ollama_client, 
    check_ollama_service, 
    start_ollama_service,
    OllamaGenerationConfig
)
from src.agents.base_agent import Principle, IntentExample
from src.evaluation.metrics import ResultsAnalyzer


class OllamaStudentAgent:
    """使用Ollama的学生智能体"""
    
    def __init__(self, agent_id: str, strategy: str, ollama_client):
        self.agent_id = agent_id
        self.strategy = strategy
        self.ollama_client = ollama_client
        self.learned_principles = []
        
    def learn_principles(self, task_description: str, examples: List[IntentExample]) -> List[Principle]:
        """学习原则"""
        examples_text = "\n".join([f"{ex.text}:{ex.label}" for ex in examples])
        
        if self.strategy == "analytical":
            prompt = self._create_analytical_prompt(examples_text)
        elif self.strategy == "pattern_based":
            prompt = self._create_pattern_prompt(examples_text)
        elif self.strategy == "linguistic":
            prompt = self._create_linguistic_prompt(examples_text)
        else:  # contextual
            prompt = self._create_contextual_prompt(examples_text)
        
        try:
            response = self.ollama_client.generate(prompt, OllamaGenerationConfig(max_tokens=1024))
            principles = self._parse_principles(response)
            self.learned_principles = principles
            return principles
        except Exception as e:
            logger.error(f"Agent {self.agent_id} 学习失败: {e}")
            return []
    
    def _create_analytical_prompt(self, examples_text: str) -> str:
        return f"""你是一个分析型AI系统。请分析以下20种技术意图的文本示例，提取区分这些意图的关键原则。

示例:
{examples_text}

请提取5个最重要的分析原则，格式如下：
PRINCIPLE 1: [原则描述]
PRINCIPLE 2: [原则描述]
...

原则应该基于技术关键词、问题模式、上下文线索等分析要素。"""

    def _create_pattern_prompt(self, examples_text: str) -> str:
        return f"""你是一个模式识别专家。请识别以下技术文本中的语言和结构模式。

示例:
{examples_text}

请提取5个最重要的模式，格式如下：
PATTERN 1: [模式描述]
PATTERN 2: [模式描述]
...

重点关注关键词模式、语法结构、技术术语等。"""

    def _create_linguistic_prompt(self, examples_text: str) -> str:
        return f"""你是一个计算语言学专家。请分析这些技术文本的语言特征。

示例:
{examples_text}

请提取5个最重要的语言特征，格式如下：
LINGUISTIC_FEATURE 1: [特征描述]
LINGUISTIC_FEATURE 2: [特征描述]
...

重点分析语义域、句法结构、命名实体模式等。"""

    def _create_contextual_prompt(self, examples_text: str) -> str:
        return f"""你是一个上下文理解专家。请分析这些技术文本的上下文指标。

示例:
{examples_text}

请提取5个最重要的上下文原则，格式如下：
CONTEXT_PRINCIPLE 1: [原则描述]
CONTEXT_PRINCIPLE 2: [原则描述]
...

重点关注问题解决场景、技术环境、用户目标等上下文信息。"""

    def _parse_principles(self, response: str) -> List[Principle]:
        """解析原则"""
        principles = []
        lines = response.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line for keyword in ['PRINCIPLE', 'PATTERN', 'LINGUISTIC_FEATURE', 'CONTEXT_PRINCIPLE']):
                if ':' in line:
                    content = line.split(':', 1)[1].strip()
                    if content:
                        principle = Principle(
                            id=f"{self.agent_id}_principle_{len(principles)+1}",
                            content=content,
                            source_agent=self.agent_id
                        )
                        principles.append(principle)
        
        return principles


class OllamaTeacherAgent:
    """使用Ollama的教师智能体"""
    
    def __init__(self, ollama_client):
        self.ollama_client = ollama_client
        
    def evaluate_and_consolidate(self, student_principles: List[List[Principle]], examples: List[IntentExample]) -> List[Principle]:
        """评估并整合原则"""
        
        # 收集所有原则
        all_principles = []
        for principle_set in student_principles:
            all_principles.extend(principle_set)
        
        if not all_principles:
            return []
        
        # 格式化原则
        principles_text = "\n".join([
            f"PRINCIPLE {i+1}: {p.content} (来源: {p.source_agent})"
            for i, p in enumerate(all_principles)
        ])
        
        examples_text = "\n".join([f"{ex.text}:{ex.label}" for ex in examples])
        
        prompt = f"""你是一个教师AI，需要评估和整合学生提出的原则。

以下是20种技术意图的示例：
{examples_text}

以下是4个学生AI提出的原则：
{principles_text}

请分析这些原则，创建一个整合的原则集合，要求：
1. 结合多个输入原则的优点
2. 消除冗余和重叠
3. 足够具体以便操作
4. 有效覆盖所有20个意图类别

请提供5个整合后的原则，格式如下：
CONSOLIDATED_PRINCIPLE 1: [原则描述]
CONSOLIDATED_PRINCIPLE 2: [原则描述]
...
"""

        try:
            response = self.ollama_client.generate(prompt, OllamaGenerationConfig(max_tokens=1024))
            return self._parse_consolidated_principles(response)
        except Exception as e:
            logger.error(f"教师智能体处理失败: {e}")
            return all_principles[:5]  # 返回前5个原则作为fallback
    
    def _parse_consolidated_principles(self, response: str) -> List[Principle]:
        """解析整合原则"""
        principles = []
        lines = response.split('\n')
        
        for line in lines:
            line = line.strip()
            if 'CONSOLIDATED_PRINCIPLE' in line and ':' in line:
                content = line.split(':', 1)[1].strip()
                if content:
                    principle = Principle(
                        id=f"teacher_consolidated_{len(principles)+1}",
                        content=content,
                        source_agent="teacher_agent",
                        confidence=1.0
                    )
                    principles.append(principle)
        
        return principles


class OllamaExamAgent:
    """使用Ollama的考试智能体"""
    
    def __init__(self, ollama_client):
        self.ollama_client = ollama_client
        
    def classify(self, texts: List[str], principles: List[Principle], intent_labels: List[str], examples: List[IntentExample]) -> List[str]:
        """分类文本"""
        
        principles_text = "\n".join([f"- {p.content}" for p in principles])
        examples_text = "\n".join([f"{ex.text}:{ex.label}" for ex in examples])
        intent_labels_str = ",".join(intent_labels)
        
        predictions = []
        
        for i, text in enumerate(texts):
            if i % 10 == 0:
                logger.info(f"分类进度: {i}/{len(texts)}")
            
            prompt = f"""你需要将以下文本分类到20个技术意图之一。

可选意图: {intent_labels_str}

参考示例:
{examples_text}

分类原则:
{principles_text}

待分类文本: {text}

基于原则和示例，这个文本最可能表达哪种意图？只回答意图标签（如"wordpress"或"oracle"）："""

            try:
                response = self.ollama_client.generate(prompt, OllamaGenerationConfig(max_tokens=50))
                prediction = self._extract_prediction(response, intent_labels)
                predictions.append(prediction)
            except Exception as e:
                logger.error(f"分类失败: {e}")
                predictions.append(intent_labels[0])  # 默认预测
        
        return predictions
    
    def _extract_prediction(self, response: str, intent_labels: List[str]) -> str:
        """提取预测结果"""
        response_lower = response.lower().strip()
        
        # 寻找匹配的标签
        for label in intent_labels:
            if label.lower() in response_lower:
                return label
        
        # 如果没找到，返回第一个标签
        return intent_labels[0]


def run_ollama_demo():
    """运行Ollama演示"""
    
    logger.info("="*60)
    logger.info("多智能体意图检测框架 - Ollama演示")
    logger.info("使用Qwen2.5-7B模型")
    logger.info("="*60)
    
    try:
        # 1. 检查Ollama服务
        if not check_ollama_service():
            logger.info("尝试启动Ollama服务...")
            if not start_ollama_service():
                logger.error("无法启动Ollama服务，请手动启动：ollama serve")
                return False
        
        # 2. 初始化Ollama客户端
        logger.info("初始化Ollama客户端...")
        ollama_client = initialize_ollama_client("qwen2.5:7b")
        
        # 3. 加载数据
        logger.info("加载数据...")
        config = load_config("config_demo.yaml")
        data_config = {
            'train_path': config.data.train_path,
            'test_path': config.data.test_path,
            'dev_path': config.data.dev_path,
            'intent_labels': config.data.intent_labels,
            'examples_per_intent': config.data.examples_per_intent,
            'max_text_length': config.data.max_text_length,
            'demo_test_samples': 50  # 减少到50个样本
        }
        
        data_processor = create_data_processor(data_config)
        logger.info(f"数据加载完成: {len(data_processor.test_data)} 测试样本")
        
        # 4. 学生智能体学习
        logger.info("\n" + "="*60)
        logger.info("阶段1: 学生智能体学习")
        logger.info("="*60)
        
        strategies = ["analytical", "pattern_based", "linguistic", "contextual"]
        student_agents = [
            OllamaStudentAgent(f"student_{i+1}", strategy, ollama_client)
            for i, strategy in enumerate(strategies)
        ]
        
        student_principles = []
        for agent in student_agents:
            logger.info(f"学生智能体 {agent.agent_id} ({agent.strategy}) 正在学习...")
            principles = agent.learn_principles("意图检测", data_processor.demonstration_examples)
            student_principles.append(principles)
            logger.info(f"  学习到 {len(principles)} 个原则")
        
        # 5. 教师智能体评估
        logger.info("\n" + "="*60)
        logger.info("阶段2: 教师智能体评估")
        logger.info("="*60)
        
        teacher_agent = OllamaTeacherAgent(ollama_client)
        consolidated_principles = teacher_agent.evaluate_and_consolidate(
            student_principles, data_processor.demonstration_examples
        )
        
        logger.info(f"教师智能体整合了 {len(consolidated_principles)} 个原则")
        logger.info("整合后的原则:")
        for i, principle in enumerate(consolidated_principles):
            logger.info(f"  {i+1}. {principle.content}")
        
        # 6. 考试智能体分类
        logger.info("\n" + "="*60)
        logger.info("阶段3: 考试智能体分类")
        logger.info("="*60)
        
        exam_agent = OllamaExamAgent(ollama_client)
        test_texts = [ex.text for ex in data_processor.test_data]
        test_labels = [ex.label for ex in data_processor.test_data]
        
        predictions = exam_agent.classify(
            test_texts, consolidated_principles, 
            config.data.intent_labels, data_processor.demonstration_examples
        )
        
        # 7. 评估结果
        logger.info("\n" + "="*60)
        logger.info("结果评估")
        logger.info("="*60)
        
        analyzer = ResultsAnalyzer(config.data.intent_labels, "ollama_results")
        results = analyzer.analyze_results(
            y_true=test_labels,
            y_pred=predictions,
            texts=test_texts,
            experiment_name="ollama_qwen_demo"
        )
        
        logger.info("🎉 Ollama演示完成!")
        return True
        
    except Exception as e:
        logger.error(f"演示失败: {e}")
        logger.exception("详细错误:")
        return False


def main():
    """主函数"""
    
    # 设置日志
    logger.remove()
    logger.add(sys.stdout, level="INFO", 
               format="{time:HH:mm:ss} | {level} | {message}")
    
    success = run_ollama_demo()
    
    if success:
        logger.info("\n🎉 演示成功完成!")
        logger.info("\n使用说明:")
        logger.info("1. 确保Ollama已安装: curl -fsSL https://ollama.com/install.sh | sh")
        logger.info("2. 启动服务: ollama serve")
        logger.info("3. 拉取模型: ollama pull qwen2.5:7b")
        logger.info("4. 运行演示: python demo_ollama.py")
    else:
        logger.error("\n❌ 演示失败!")
        sys.exit(1)


if __name__ == "__main__":
    main()
