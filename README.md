# Multi-Agent Intent Detection Framework

A state-of-the-art multi-agent framework for intent detection inspired by the learning-testing paradigm. The framework treats classification as a multiple-choice problem and uses four student agents, one teacher agent, and one exam agent to achieve high accuracy on technical intent classification tasks.

## Architecture

### Framework Overview
The framework implements a learning-testing paradigm with the following components:

1. **Four Student Agents**: Learn principles from examples using different strategies
   - Analytical Agent: Uses analytical approach to extract principles
   - Pattern-based Agent: Focuses on pattern recognition
   - Linguistic Agent: Analyzes linguistic features
   - Contextual Agent: Emphasizes contextual understanding

2. **Teacher Agent**: Evaluates and consolidates principles using mathematical framework
   - Implements evaluation formula: S_k = Σ(i=1 to P) w_i * f_i(R_k)
   - Two evaluation dimensions: Adequacy and Clarity (weights: 0.5 each)
   - Quality threshold: 0.7 for principle selection

3. **Exam Agent**: Performs downstream classification using learned principles
   - Supports batch processing and chain-of-thought reasoning
   - Uses consolidated principles for classification decisions

## Features

- **SOTA Implementation**: Uses vLLM with Qwen2.5-7B model for high-performance inference
- **GPU Support**: Distributed deployment on GPU0 and GPU1 with tensor parallelism
- **Mathematical Evaluation**: Rigorous principle evaluation with mathematical framework
- **Comprehensive Metrics**: Accuracy, F1 (macro/micro), Precision, Recall
- **Visualization**: Confusion matrices, per-class metrics, error analysis
- **Configurable**: YAML-based configuration with extensive customization options

## Installation

1. **Clone the repository**:
```bash
git clone <repository-url>
cd multi-agent-intent-detection
```

2. **Install dependencies**:
```bash
pip install -r requirements.txt
```

3. **Verify GPU setup**:
```bash
python main.py --gpu-check
```

## Quick Start

### Basic Usage
```bash
# Run full pipeline with default configuration
python main.py

# Run with custom configuration
python main.py --config custom_config.yaml

# Run with verbose logging
python main.py --verbose
```

### Advanced Usage
```bash
# Run only learning phase
python main.py --learning-only

# Run only testing phase (requires existing principles)
python main.py --testing-only --principles-file results/consolidated_principles.json

# Custom output directory
python main.py --output-dir my_experiment
```

## Configuration

The framework uses YAML configuration files. Key configuration sections:

### Model Configuration
```yaml
model:
  name: "Qwen/Qwen2.5-7B-Instruct"
  hf_mirror: "https://hf-mirror.com"  # China mainland mirror
  temperature: 0.2
  top_p: 0.9
  gpu_devices: [0, 1]  # GPU0 and GPU1
  tensor_parallel_size: 2
```

### Agent Configuration
```yaml
agents:
  student:
    count: 4
    strategies: ["analytical", "pattern_based", "linguistic", "contextual"]
  teacher:
    evaluation_dimensions: 2
    dimension_weights: [0.5, 0.5]
    quality_threshold: 0.7
  exam:
    batch_size: 32
```

## Data Format

The framework expects TSV files with the following format:
```
text	label
How to configure Apache server?	apache
WordPress plugin development	wordpress
Oracle database connection	oracle
```

Supported intent labels:
- wordpress, oracle, svn, apache, excel, matlab
- visual-studio, cocoa, osx, bash, spring, hibernate
- scala, sharepoint, ajax, qt, drupal, linq, haskell, magento

## Results and Evaluation

### Output Files
- `complete_results.json`: Full experiment results
- `student_principles.json`: Principles learned by student agents
- `consolidated_principles.json`: Final consolidated principles
- `*_confusion_matrix.png`: Confusion matrix visualization
- `*_f1_scores.png`: Per-class F1 score visualization

### Metrics
- **Accuracy**: Overall classification accuracy
- **F1 Scores**: Macro, micro, and weighted F1 scores
- **Precision/Recall**: Macro and micro averages
- **Per-class Metrics**: Individual class performance
- **Error Analysis**: Confusion patterns and error distribution

## Technical Details

### Mathematical Framework
The teacher agent uses a mathematical evaluation framework:

**Overall Score**: S_k = Σ(i=1 to P) w_i * f_i(R_k)

Where:
- P = 2 (evaluation dimensions)
- w_i = weights [0.5, 0.5] for adequacy and clarity
- f_i = normalized scoring functions
- R_k = principle k

### GPU Deployment
- **Model**: Qwen2.5-7B-Instruct via vLLM
- **Distribution**: Tensor parallelism across GPU0 and GPU1
- **Memory**: 90% GPU memory utilization
- **Inference**: Temperature=0.2, top_p=0.9

### Performance Optimizations
- Batch processing for efficient inference
- Parallel student agent execution
- Optimized prompt engineering
- Intermediate result caching

## Troubleshooting

### Common Issues

1. **GPU Memory Error**:
   - Reduce `batch_size` in configuration
   - Lower `gpu_memory_utilization` in vLLM settings

2. **Model Loading Error**:
   - Check HuggingFace mirror configuration
   - Verify model name and availability

3. **Data Loading Error**:
   - Ensure TSV files have correct format
   - Check file paths in configuration

### Logging
Logs are saved to `logs/framework.log` with rotation. Use `--verbose` for debug-level logging.

## Citation

If you use this framework in your research, please cite:

```bibtex
@misc{multi-agent-intent-detection,
  title={Multi-Agent Intent Detection Framework: A Learning-Testing Paradigm},
  author={Your Name},
  year={2024},
  url={https://github.com/your-repo/multi-agent-intent-detection}
}
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For questions and support:
- Create an issue on GitHub
- Check the documentation
- Review the configuration examples
