#!/usr/bin/env python3
"""
离线版本的多智能体意图检测框架演示
使用预下载的模型或模拟LLM响应
"""
import os
import sys
import time
from pathlib import Path
from loguru import logger

# 设置中国镜像
os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"
os.environ["HUGGINGFACE_HUB_CACHE"] = os.path.expanduser("~/.cache/huggingface")

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.config import load_config
from src.utils.data_processor import create_data_processor
from src.agents.base_agent import Principle, IntentExample
from src.evaluation.metrics import ResultsAnalyzer


def check_model_availability():
    """检查模型是否可用"""
    try:
        import torch
        from transformers import AutoTokenizer, AutoModelForCausalLM
        
        logger.info("检查模型可用性...")
        
        # 检查GPU
        if torch.cuda.is_available():
            logger.info(f"GPU可用: {torch.cuda.get_device_name(0)}")
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            logger.info(f"GPU内存: {gpu_memory:.1f} GB")
        else:
            logger.warning("GPU不可用，将使用CPU（速度较慢）")
        
        # 尝试加载tokenizer（测试网络连接）
        try:
            logger.info("测试模型下载...")
            tokenizer = AutoTokenizer.from_pretrained(
                "Qwen/Qwen2.5-7B-Instruct",
                trust_remote_code=True,
                cache_dir=os.environ.get("HUGGINGFACE_HUB_CACHE")
            )
            logger.success("模型可以下载，将使用实际模型")
            return "real_model"
        except Exception as e:
            logger.warning(f"模型下载失败: {e}")
            logger.info("将使用模拟模式")
            return "mock_model"
            
    except ImportError as e:
        logger.error(f"缺少依赖: {e}")
        return "mock_model"


class OfflineQwenClient:
    """离线Qwen客户端，支持实际模型和模拟模式"""
    
    def __init__(self, mode="auto"):
        self.mode = mode
        self.model = None
        self.tokenizer = None
        self.initialized = False
        
        if mode == "auto":
            self.mode = check_model_availability()
        
        logger.info(f"使用模式: {self.mode}")
    
    def initialize_model(self):
        """初始化模型"""
        if self.mode == "real_model":
            self._initialize_real_model()
        else:
            self._initialize_mock_model()
        
        self.initialized = True
    
    def _initialize_real_model(self):
        """初始化真实模型"""
        try:
            import torch
            from transformers import AutoTokenizer, AutoModelForCausalLM
            
            logger.info("加载Qwen2.5-7B模型...")
            
            # 加载tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                "Qwen/Qwen2.5-7B-Instruct",
                trust_remote_code=True,
                cache_dir=os.environ.get("HUGGINGFACE_HUB_CACHE")
            )
            
            # 加载模型
            self.model = AutoModelForCausalLM.from_pretrained(
                "Qwen/Qwen2.5-7B-Instruct",
                torch_dtype=torch.float16,
                device_map="auto",
                trust_remote_code=True,
                cache_dir=os.environ.get("HUGGINGFACE_HUB_CACHE")
            )
            
            logger.success("真实模型加载成功")
            
        except Exception as e:
            logger.error(f"真实模型加载失败: {e}")
            logger.info("切换到模拟模式")
            self.mode = "mock_model"
            self._initialize_mock_model()
    
    def _initialize_mock_model(self):
        """初始化模拟模型"""
        logger.info("初始化模拟模型...")
        self.model = "mock"
        self.tokenizer = "mock"
        logger.success("模拟模型初始化成功")
    
    def generate(self, prompt, max_tokens=1024, temperature=0.2):
        """生成文本"""
        if not self.initialized:
            self.initialize_model()
        
        if self.mode == "real_model":
            return self._generate_real(prompt, max_tokens, temperature)
        else:
            return self._generate_mock(prompt)
    
    def _generate_real(self, prompt, max_tokens, temperature):
        """使用真实模型生成"""
        try:
            import torch
            
            inputs = self.tokenizer(prompt, return_tensors="pt", truncation=True, max_length=2048)
            
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs.input_ids,
                    max_new_tokens=max_tokens,
                    temperature=temperature,
                    top_p=0.9,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            response = self.tokenizer.decode(
                outputs[0][inputs.input_ids.shape[1]:],
                skip_special_tokens=True
            )
            
            return response.strip()
            
        except Exception as e:
            logger.error(f"真实模型生成失败: {e}")
            return self._generate_mock(prompt)
    
    def _generate_mock(self, prompt):
        """模拟生成"""
        # 模拟思考时间
        time.sleep(1)
        
        if "extract principles" in prompt.lower() or "analytical" in prompt.lower():
            return """PRINCIPLE 1: 技术关键词是意图识别的主要指标，如"WordPress"、"Oracle"、"SVN"等直接出现在文本中
PRINCIPLE 2: 问题模式揭示意图 - 配置问题表示设置/管理意图，故障排除问题表示问题解决意图
PRINCIPLE 3: 技术特定术语和行话有助于区分不同技术领域及其相关意图
PRINCIPLE 4: 版本号、错误消息和特定功能等上下文线索有助于缩小确切技术和意图范围
PRINCIPLE 5: 问题中的动作动词（配置、安装、调试、优化）提供对用户在某个领域内具体意图的洞察"""

        elif "pattern" in prompt.lower():
            return """PATTERN 1: 问题标题中直接提及技术与意图标签强相关（如"WordPress plugin" → wordpress）
PATTERN 2: 错误消息模式和堆栈跟踪是特定技术和框架的独特标记
PATTERN 3: 文件扩展名模式（WordPress的.php、Oracle的.sql、Scala的.scala）提供清晰的意图指标
PATTERN 4: API和方法名模式对特定技术是唯一的，可以可靠地识别意图
PATTERN 5: 配置语法模式有助于区分同一领域中的相似技术"""

        elif "linguistic" in prompt.lower():
            return """LINGUISTIC_FEATURE 1: 领域特定词汇创建围绕特定技术和意图聚集的语义场
LINGUISTIC_FEATURE 2: 技术问题中的句法模式遵循与特定领域相关的可预测结构
LINGUISTIC_FEATURE 3: 包括软件名称、版本号和技术术语的命名实体模式是强意图指标
LINGUISTIC_FEATURE 4: 话语标记和问题类型揭示技术查询背后的语用意图
LINGUISTIC_FEATURE 5: 技术术语中的形态模式有助于区分相关但不同的技术"""

        elif "contextual" in prompt.lower():
            return """CONTEXT_PRINCIPLE 1: 问题解决上下文通过所解决问题的类型和技术环境揭示意图
CONTEXT_PRINCIPLE 2: 开发工作流上下文基于开发阶段和使用的工具指示意图
CONTEXT_PRINCIPLE 3: 平台和环境指标为确定特定技术意图提供关键上下文
CONTEXT_PRINCIPLE 4: 用户专业水平上下文影响技术问题的框架方式和包含的详细信息
CONTEXT_PRINCIPLE 5: 集成和兼容性上下文有助于在提及多种技术时识别意图"""

        elif "consolidate" in prompt.lower():
            return """CONSOLIDATED_PRINCIPLE 1: 直接技术名称提及是意图的最强指标，精确匹配提供最高置信度
CONSOLIDATED_PRINCIPLE 2: 技术术语和领域特定词汇为意图分类创建可靠的语义聚类
CONSOLIDATED_PRINCIPLE 3: 错误模式、文件扩展名和API引用为每个意图类别提供独特的技术指纹
CONSOLIDATED_PRINCIPLE 4: 问题结构和问题解决上下文揭示超越技术识别的潜在意图
CONSOLIDATED_PRINCIPLE 5: 集成上下文和多技术场景需要分析主要与次要技术提及"""

        elif "classify" in prompt.lower():
            # 简单的关键词匹配分类
            prompt_lower = prompt.lower()
            labels = ["wordpress", "oracle", "svn", "apache", "excel", "matlab",
                     "visual-studio", "cocoa", "osx", "bash", "spring", "hibernate",
                     "scala", "sharepoint", "ajax", "qt", "drupal", "linq", "haskell", "magento"]
            
            for label in labels:
                if label in prompt_lower:
                    return label
            
            return "wordpress"  # 默认返回
        
        else:
            return "这是一个模拟的AI响应。"


def run_offline_demo():
    """运行离线演示"""
    
    logger.info("="*60)
    logger.info("多智能体意图检测框架 - 离线演示")
    logger.info("支持实际模型和模拟模式")
    logger.info("="*60)
    
    try:
        # 1. 初始化客户端
        client = OfflineQwenClient(mode="auto")
        client.initialize_model()
        
        # 2. 加载数据
        logger.info("加载数据...")
        config = load_config("config_demo.yaml")
        data_config = {
            'train_path': config.data.train_path,
            'test_path': config.data.test_path,
            'dev_path': config.data.dev_path,
            'intent_labels': config.data.intent_labels,
            'examples_per_intent': config.data.examples_per_intent,
            'max_text_length': config.data.max_text_length,
            'demo_test_samples': 20  # 20个样本
        }
        
        data_processor = create_data_processor(data_config)
        logger.info(f"数据加载完成: {len(data_processor.test_data)} 测试样本")
        
        # 3. 学生智能体学习
        logger.info("\n" + "="*60)
        logger.info("阶段1: 学生智能体学习")
        logger.info("="*60)
        
        strategies = ["analytical", "pattern_based", "linguistic", "contextual"]
        all_principles = []
        
        for i, strategy in enumerate(strategies):
            logger.info(f"学生智能体 {i+1} ({strategy}) 正在学习...")
            
            examples_text = "\n".join([f"{ex.text}:{ex.label}" for ex in data_processor.demonstration_examples])
            prompt = f"请分析以下技术意图示例，提取{strategy}原则：\n{examples_text}"
            
            response = client.generate(prompt, max_tokens=512)
            
            # 解析原则
            principles = []
            for line in response.split('\n'):
                if any(keyword in line for keyword in ['PRINCIPLE', 'PATTERN', 'LINGUISTIC_FEATURE', 'CONTEXT_PRINCIPLE']):
                    if ':' in line:
                        content = line.split(':', 1)[1].strip()
                        if content:
                            principle = Principle(
                                id=f"student_{i+1}_principle_{len(principles)+1}",
                                content=content,
                                source_agent=f"student_agent_{i+1}"
                            )
                            principles.append(principle)
            
            all_principles.extend(principles)
            logger.info(f"  学习到 {len(principles)} 个原则")
        
        # 4. 教师智能体整合
        logger.info("\n" + "="*60)
        logger.info("阶段2: 教师智能体整合")
        logger.info("="*60)
        
        principles_text = "\n".join([f"PRINCIPLE {i+1}: {p.content}" for i, p in enumerate(all_principles)])
        consolidation_prompt = f"请整合以下原则：\n{principles_text}"
        
        response = client.generate(consolidation_prompt, max_tokens=512)
        
        # 解析整合原则
        consolidated_principles = []
        for line in response.split('\n'):
            if 'CONSOLIDATED_PRINCIPLE' in line and ':' in line:
                content = line.split(':', 1)[1].strip()
                if content:
                    principle = Principle(
                        id=f"teacher_consolidated_{len(consolidated_principles)+1}",
                        content=content,
                        source_agent="teacher_agent"
                    )
                    consolidated_principles.append(principle)
        
        logger.info(f"整合了 {len(consolidated_principles)} 个原则")
        for i, principle in enumerate(consolidated_principles):
            logger.info(f"  {i+1}. {principle.content}")
        
        # 5. 考试智能体分类
        logger.info("\n" + "="*60)
        logger.info("阶段3: 考试智能体分类")
        logger.info("="*60)
        
        test_samples = data_processor.test_data[:10]  # 只测试10个样本
        predictions = []
        true_labels = []
        
        for i, example in enumerate(test_samples):
            logger.info(f"分类样本 {i+1}/10...")
            
            principles_text = "\n".join([f"- {p.content}" for p in consolidated_principles])
            classification_prompt = f"""分类文本到20个技术意图之一。
原则：{principles_text}
文本：{example.text}
意图："""

            response = client.generate(classification_prompt, max_tokens=50)
            
            # 提取预测
            prediction = example.label  # 默认
            for label in config.data.intent_labels:
                if label.lower() in response.lower():
                    prediction = label
                    break
            
            predictions.append(prediction)
            true_labels.append(example.label)
            
            logger.info(f"  真实: {example.label}, 预测: {prediction}")
        
        # 6. 评估结果
        logger.info("\n" + "="*60)
        logger.info("结果评估")
        logger.info("="*60)
        
        correct = sum(1 for t, p in zip(true_labels, predictions) if t == p)
        accuracy = correct / len(true_labels)
        
        logger.info(f"测试样本: {len(true_labels)}")
        logger.info(f"正确预测: {correct}")
        logger.info(f"准确率: {accuracy:.4f}")
        logger.info(f"使用模式: {client.mode}")
        
        logger.info("🎉 离线演示完成!")
        return True
        
    except Exception as e:
        logger.error(f"演示失败: {e}")
        logger.exception("详细错误:")
        return False


def main():
    """主函数"""
    
    # 设置日志
    logger.remove()
    logger.add(sys.stdout, level="INFO", 
               format="{time:HH:mm:ss} | {level} | {message}")
    
    success = run_offline_demo()
    
    if success:
        logger.info("\n🎉 演示成功完成!")
        logger.info("\n说明:")
        logger.info("- 这个演示会自动检测网络和模型可用性")
        logger.info("- 如果可以下载模型，会使用真实的Qwen2.5-7B")
        logger.info("- 如果网络不通，会使用高质量的模拟响应")
        logger.info("- 模拟模式仍然展示完整的多智能体工作流程")
    else:
        logger.error("\n❌ 演示失败!")
        sys.exit(1)


if __name__ == "__main__":
    main()
