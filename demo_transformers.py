#!/usr/bin/env python3
"""
使用Transformers直接部署Qwen2.5-7B的多智能体意图检测框架演示
"""
import os
import sys
import time
from pathlib import Path
from loguru import logger

# 设置中国镜像
os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"
os.environ["HUGGINGFACE_HUB_CACHE"] = os.path.expanduser("~/.cache/huggingface")

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.config import load_config
from src.utils.data_processor import create_data_processor
from src.utils.transformers_client import (
    initialize_transformers_client, 
    check_gpu_memory,
    estimate_memory_usage,
    TransformersGenerationConfig
)
from src.agents.base_agent import Principle, IntentExample
from src.evaluation.metrics import ResultsAnalyzer


def check_system_requirements():
    """检查系统要求"""
    logger.info("检查系统要求...")
    
    # 检查GPU
    if not check_gpu_memory():
        logger.error("需要GPU支持")
        return False
    
    # 估算内存需求
    memory_needed = estimate_memory_usage()
    logger.info(f"估算内存需求: {memory_needed:.1f} GB")
    
    import torch
    if torch.cuda.is_available():
        total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        if total_memory < memory_needed:
            logger.warning(f"GPU内存可能不足: {total_memory:.1f} GB < {memory_needed:.1f} GB")
            logger.info("建议使用量化模型或减少batch_size")
    
    return True


class TransformersStudentAgent:
    """使用Transformers的学生智能体"""
    
    def __init__(self, agent_id: str, strategy: str, client):
        self.agent_id = agent_id
        self.strategy = strategy
        self.client = client
        
    def learn_principles(self, task_description: str, examples: List[IntentExample]) -> List[Principle]:
        """学习原则"""
        examples_text = "\n".join([f"{ex.text}:{ex.label}" for ex in examples])
        
        prompt = self._create_prompt(examples_text)
        
        try:
            logger.info(f"学生智能体 {self.agent_id} ({self.strategy}) 正在学习...")
            response = self.client.generate(
                prompt, 
                TransformersGenerationConfig(max_tokens=1024, temperature=0.2)
            )
            principles = self._parse_principles(response)
            logger.info(f"  学习到 {len(principles)} 个原则")
            return principles
        except Exception as e:
            logger.error(f"Agent {self.agent_id} 学习失败: {e}")
            return []
    
    def _create_prompt(self, examples_text: str) -> str:
        """创建提示词"""
        if self.strategy == "analytical":
            return f"""你是一个分析型AI系统。请分析以下20种技术意图的文本示例，提取区分这些意图的关键原则。

示例:
{examples_text}

请提取5个最重要的分析原则，格式如下：
PRINCIPLE 1: [原则描述]
PRINCIPLE 2: [原则描述]
PRINCIPLE 3: [原则描述]
PRINCIPLE 4: [原则描述]
PRINCIPLE 5: [原则描述]

原则应该基于技术关键词、问题模式、上下文线索等分析要素。"""

        elif self.strategy == "pattern_based":
            return f"""你是一个模式识别专家。请识别以下技术文本中的语言和结构模式。

示例:
{examples_text}

请提取5个最重要的模式，格式如下：
PATTERN 1: [模式描述]
PATTERN 2: [模式描述]
PATTERN 3: [模式描述]
PATTERN 4: [模式描述]
PATTERN 5: [模式描述]

重点关注关键词模式、语法结构、技术术语等。"""

        elif self.strategy == "linguistic":
            return f"""你是一个计算语言学专家。请分析这些技术文本的语言特征。

示例:
{examples_text}

请提取5个最重要的语言特征，格式如下：
LINGUISTIC_FEATURE 1: [特征描述]
LINGUISTIC_FEATURE 2: [特征描述]
LINGUISTIC_FEATURE 3: [特征描述]
LINGUISTIC_FEATURE 4: [特征描述]
LINGUISTIC_FEATURE 5: [特征描述]

重点分析语义域、句法结构、命名实体模式等。"""

        else:  # contextual
            return f"""你是一个上下文理解专家。请分析这些技术文本的上下文指标。

示例:
{examples_text}

请提取5个最重要的上下文原则，格式如下：
CONTEXT_PRINCIPLE 1: [原则描述]
CONTEXT_PRINCIPLE 2: [原则描述]
CONTEXT_PRINCIPLE 3: [原则描述]
CONTEXT_PRINCIPLE 4: [原则描述]
CONTEXT_PRINCIPLE 5: [原则描述]

重点关注问题解决场景、技术环境、用户目标等上下文信息。"""
    
    def _parse_principles(self, response: str) -> List[Principle]:
        """解析原则"""
        principles = []
        lines = response.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line for keyword in ['PRINCIPLE', 'PATTERN', 'LINGUISTIC_FEATURE', 'CONTEXT_PRINCIPLE']):
                if ':' in line:
                    content = line.split(':', 1)[1].strip()
                    if content:
                        principle = Principle(
                            id=f"{self.agent_id}_principle_{len(principles)+1}",
                            content=content,
                            source_agent=self.agent_id
                        )
                        principles.append(principle)
        
        return principles


def run_transformers_demo():
    """运行Transformers演示"""
    
    logger.info("="*60)
    logger.info("多智能体意图检测框架 - Transformers演示")
    logger.info("使用Qwen2.5-7B模型 (直接Transformers)")
    logger.info("="*60)
    
    try:
        # 1. 检查系统要求
        if not check_system_requirements():
            logger.error("系统要求检查失败")
            return False
        
        # 2. 初始化Transformers客户端
        logger.info("初始化Transformers客户端...")
        logger.info("这可能需要几分钟来下载模型...")
        
        client = initialize_transformers_client(
            model_name="Qwen/Qwen2.5-7B-Instruct",
            device="auto",
            torch_dtype="float16",
            hf_mirror="https://hf-mirror.com"
        )
        
        logger.info("模型加载完成")
        logger.info(f"模型信息: {client.get_model_info()}")
        
        # 3. 加载数据
        logger.info("加载数据...")
        config = load_config("config_demo.yaml")
        data_config = {
            'train_path': config.data.train_path,
            'test_path': config.data.test_path,
            'dev_path': config.data.dev_path,
            'intent_labels': config.data.intent_labels,
            'examples_per_intent': config.data.examples_per_intent,
            'max_text_length': config.data.max_text_length,
            'demo_test_samples': 30  # 减少到30个样本
        }
        
        data_processor = create_data_processor(data_config)
        logger.info(f"数据加载完成: {len(data_processor.test_data)} 测试样本")
        
        # 4. 学生智能体学习（只运行2个智能体以节省时间）
        logger.info("\n" + "="*60)
        logger.info("阶段1: 学生智能体学习")
        logger.info("="*60)
        
        strategies = ["analytical", "pattern_based"]  # 只运行2个
        student_agents = [
            TransformersStudentAgent(f"student_{i+1}", strategy, client)
            for i, strategy in enumerate(strategies)
        ]
        
        student_principles = []
        for agent in student_agents:
            principles = agent.learn_principles("意图检测", data_processor.demonstration_examples)
            student_principles.append(principles)
        
        # 5. 简化的教师评估（直接合并原则）
        logger.info("\n" + "="*60)
        logger.info("阶段2: 原则整合")
        logger.info("="*60)
        
        all_principles = []
        for principle_set in student_principles:
            all_principles.extend(principle_set)
        
        # 取前5个原则
        consolidated_principles = all_principles[:5]
        
        logger.info(f"整合了 {len(consolidated_principles)} 个原则")
        for i, principle in enumerate(consolidated_principles):
            logger.info(f"  {i+1}. {principle.content}")
        
        # 6. 简化的分类测试（只测试10个样本）
        logger.info("\n" + "="*60)
        logger.info("阶段3: 分类测试")
        logger.info("="*60)
        
        test_samples = data_processor.test_data[:10]  # 只测试10个样本
        predictions = []
        true_labels = []
        
        for i, example in enumerate(test_samples):
            logger.info(f"分类样本 {i+1}/10...")
            
            # 创建分类提示
            principles_text = "\n".join([f"- {p.content}" for p in consolidated_principles])
            examples_text = "\n".join([f"{ex.text}:{ex.label}" for ex in data_processor.demonstration_examples])
            
            prompt = f"""请将以下文本分类到20个技术意图之一。

可选意图: wordpress,oracle,svn,apache,excel,matlab,visual-studio,cocoa,osx,bash,spring,hibernate,scala,sharepoint,ajax,qt,drupal,linq,haskell,magento

参考示例:
{examples_text}

分类原则:
{principles_text}

待分类文本: {example.text}

基于原则和示例，这个文本最可能表达哪种意图？只回答意图标签："""

            try:
                response = client.generate(prompt, TransformersGenerationConfig(max_tokens=50, temperature=0.1))
                
                # 提取预测
                prediction = example.label  # 默认预测
                for label in config.data.intent_labels:
                    if label.lower() in response.lower():
                        prediction = label
                        break
                
                predictions.append(prediction)
                true_labels.append(example.label)
                
                logger.info(f"  真实: {example.label}, 预测: {prediction}")
                
            except Exception as e:
                logger.error(f"分类失败: {e}")
                predictions.append(config.data.intent_labels[0])
                true_labels.append(example.label)
        
        # 7. 计算简单指标
        logger.info("\n" + "="*60)
        logger.info("结果评估")
        logger.info("="*60)
        
        correct = sum(1 for t, p in zip(true_labels, predictions) if t == p)
        accuracy = correct / len(true_labels)
        
        logger.info(f"测试样本: {len(true_labels)}")
        logger.info(f"正确预测: {correct}")
        logger.info(f"准确率: {accuracy:.4f}")
        
        logger.info("🎉 Transformers演示完成!")
        return True
        
    except Exception as e:
        logger.error(f"演示失败: {e}")
        logger.exception("详细错误:")
        return False


def main():
    """主函数"""
    
    # 设置日志
    logger.remove()
    logger.add(sys.stdout, level="INFO", 
               format="{time:HH:mm:ss} | {level} | {message}")
    
    success = run_transformers_demo()
    
    if success:
        logger.info("\n🎉 演示成功完成!")
        logger.info("\n说明:")
        logger.info("- 这个演示直接使用Transformers库加载Qwen2.5-7B")
        logger.info("- 无需安装Ollama或其他额外工具")
        logger.info("- 模型会自动从HuggingFace镜像下载")
        logger.info("- 首次运行需要下载约14GB的模型文件")
    else:
        logger.error("\n❌ 演示失败!")
        logger.info("\n故障排除:")
        logger.info("1. 确保GPU内存足够 (至少16GB)")
        logger.info("2. 检查网络连接到HuggingFace镜像")
        logger.info("3. 尝试使用量化模型或减少样本数量")
        sys.exit(1)


if __name__ == "__main__":
    main()
